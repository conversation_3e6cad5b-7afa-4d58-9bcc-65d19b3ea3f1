{"version": 3, "sources": ["../../@chakra-ui/react-context/src/index.ts"], "sourcesContent": ["import {\n  createContext as createReactContext,\n  useContext as useReactContext,\n} from \"react\"\n\nexport interface CreateContextOptions<T> {\n  strict?: boolean\n  hookName?: string\n  providerName?: string\n  errorMessage?: string\n  name?: string\n  defaultValue?: T\n}\n\nexport type CreateContextReturn<T> = [\n  React.Provider<T>,\n  () => T,\n  React.Context<T>,\n]\n\nfunction getErrorMessage(hook: string, provider: string) {\n  return `${hook} returned \\`undefined\\`. Seems you forgot to wrap component within ${provider}`\n}\n\nexport function createContext<T>(options: CreateContextOptions<T> = {}) {\n  const {\n    name,\n    strict = true,\n    hookName = \"useContext\",\n    providerName = \"Provider\",\n    errorMessage,\n    defaultValue,\n  } = options\n\n  const Context = createReactContext<T | undefined>(defaultValue)\n\n  Context.displayName = name\n\n  function useContext() {\n    const context = useReactContext(Context)\n\n    if (!context && strict) {\n      const error = new Error(\n        errorMessage ?? getErrorMessage(hookName, providerName),\n      )\n      error.name = \"ContextError\"\n      Error.captureStackTrace?.(error, useContext)\n      throw error\n    }\n\n    return context\n  }\n\n  return [Context.Provider, useContext, Context] as CreateContextReturn<T>\n}\n"], "mappings": ";;;;;;;;AAAA,mBAGO;AAiBP,SAAS,gBAAgB,MAAc,UAAkB;AACvD,SAAO,GAAG,IAAA,sEAA0E,QAAA;AACtF;AAEO,SAAS,cAAiB,UAAmC,CAAC,GAAG;AACtE,QAAM;IACJ;IACA,SAAS;IACT,WAAW;IACX,eAAe;IACf;IACA;EACF,IAAI;AAEJ,QAAM,cAAU,aAAAA,eAAkC,YAAY;AAE9D,UAAQ,cAAc;AAEtB,WAAS,aAAa;AAtCxB,QAAA;AAuCI,UAAM,cAAU,aAAAC,YAAgB,OAAO;AAEvC,QAAI,CAAC,WAAW,QAAQ;AACtB,YAAM,QAAQ,IAAI;QAChB,gBAAA,OAAA,eAAgB,gBAAgB,UAAU,YAAY;MACxD;AACA,YAAM,OAAO;AACb,OAAA,KAAA,MAAM,sBAAN,OAAA,SAAA,GAAA,KAAA,OAA0B,OAAO,UAAA;AACjC,YAAM;IACR;AAEA,WAAO;EACT;AAEA,SAAO,CAAC,QAAQ,UAAU,YAAY,OAAO;AAC/C;", "names": ["createReactContext", "useReactContext"]}