/**
 * Performance Optimizations CSS
 * This file contains optimizations to improve UI performance by reducing unnecessary animations,
 * transitions, and effects that can cause sluggishness.
 */

/* Disable animations and transitions when window is resizing to prevent layout thrashing */
.resize-animation-stopper * {
  animation: none !important;
  transition: none !important;
}

/* Optimize rendering for message containers */
.message-container {
  contain: content;
  will-change: transform;
  transform: translateZ(0);
}







/* Optimize rendering for message items */
.message-item {
  contain: content;
  will-change: opacity;
  transform: translateZ(0);
  /* Ensure proper spacing between messages */
  margin-bottom: 2px;
}

/* Reduce animation complexity for new messages */
.message-new {
  animation: fadeIn 0.3s ease-out forwards !important;
  animation-fill-mode: both !important;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Optimize modal rendering */
.chakra-modal__content {
  will-change: opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Optimize image rendering */
.optimized-image {
  contain: paint;
  will-change: opacity;
  transform: translateZ(0);
}

/* Disable hover effects on mobile devices to improve performance */
@media (max-width: 768px) {
  .hover-effect {
    transition: none !important;
  }
  
  .hover-effect:hover {
    transform: none !important;
    box-shadow: none !important;
  }
}
