import { Button, Flex, Image, Box, Tooltip, useColorModeValue } from "@chakra-ui/react";
import { useRecoilValue } from "recoil";
import { userAtom } from "../atoms";
import { Link as RouterLink, useLocation } from "react-router-dom";
import { House, User, Chat, Gear, MagnifyingGlass, Bell } from "phosphor-react";
import { useBreakpointValue } from "@chakra-ui/react";
import { useState, useEffect } from "react";

const Header = () => {
	const user = useRecoilValue(userAtom);
	const location = useLocation();

	// Adjust positioning based on screen size with new breakpoints
	const isMobile = useBreakpointValue({ base: true, xs: true, sm: true, md: true, lg: true, xl: false });
	const isVerySmall = useBreakpointValue({ base: true, xs: true, sm: false });
	const isSmallMobile = useBreakpointValue({ base: true, xs: true, sm: true, md: false });

	// State for logo visibility on mobile scroll
	const [isLogoVisible, setIsLogoVisible] = useState(true);
	const [lastScrollY, setLastScrollY] = useState(0);

	// Theme-aware colors - ALL hooks must be called at the top level
	const bgColor = useColorModeValue("rgba(247, 250, 252, 0.9)", "rgba(8, 8, 8, 0.9)");
	const borderColor = useColorModeValue("rgba(0, 0, 0, 0.05)", "rgba(255, 255, 255, 0.05)");
	const iconColor = useColorModeValue("gray.700", "white");
	const logoFilter = useColorModeValue("none", "none");

	// Additional theme-aware colors for conditional usage
	const logoBgColor = useColorModeValue("rgba(247, 250, 252, 0.9)", "rgba(8, 8, 8, 0.7)");
	const logoDropShadow = useColorModeValue(
		"drop-shadow(0 0 2px rgba(0, 0, 0, 0.3))",
		"drop-shadow(0 0 2px rgba(0, 0, 0, 0.5))"
	);
	const navIconColor = useColorModeValue("#4a5568", "#616161");

	// Scroll handler for mobile logo hiding
	useEffect(() => {
		if (!isMobile) return; // Only apply scroll behavior on mobile

		const handleScroll = () => {
			const currentScrollY = window.scrollY;

			// Hide logo when scrolling down past 50px, show when scrolling back up or near top
			if (currentScrollY > 50 && currentScrollY > lastScrollY) {
				// Scrolling down and past threshold
				setIsLogoVisible(false);
			} else if (currentScrollY < lastScrollY || currentScrollY <= 30) {
				// Scrolling up or near top
				setIsLogoVisible(true);
			}

			setLastScrollY(currentScrollY);
		};

		// Add scroll listener with throttling for performance
		let ticking = false;
		const throttledHandleScroll = () => {
			if (!ticking) {
				requestAnimationFrame(() => {
					handleScroll();
					ticking = false;
				});
				ticking = true;
			}
		};

		window.addEventListener('scroll', throttledHandleScroll, { passive: true });

		return () => {
			window.removeEventListener('scroll', throttledHandleScroll);
		};
	}, [isMobile, lastScrollY]);

	// Function to check if a path is active
	const isActive = (path) => {
		if (path === "/") {
			return location.pathname === "/";
		}
		if (path.startsWith("/:username")) {
			// For user profile page, check if we're on a user page but not on a specific post
			return location.pathname.match(/^\/[^/]+$/) && location.pathname !== "/search" &&
				location.pathname !== "/notifications" && location.pathname !== "/chat" &&
				location.pathname !== "/settings" && location.pathname !== "/auth";
		}
		return location.pathname.startsWith(path);
	};

	return (
		<Flex justifyContent="space-between" align="center" mt={{ base: 4, sm: 5, xl: 6 }} mb={{ base: 8, sm: 10, xl: 12 }}>
			{/* App Logo with glow effect - Responsive positioning */}
			<Flex
				position="fixed"
				top={{ base: 3, sm: 4 }}
				left={{ base: "50%", xs: "50%", sm: "50%", md: "50%", lg: "50%", xl: 8 }}
				transform={{
					base: `translateX(-50%) ${isLogoVisible || !isMobile ? 'translateY(0)' : 'translateY(-100px)'}`,
					xl: "translateX(0)"
				}}
				opacity={{ base: isLogoVisible || !isMobile ? 1 : 0, md: 1 }}
				transition="all 0.3s ease-in-out"
				zIndex={1000} // Increased z-index to ensure logo is always on top
				className="mobile-logo-scroll"
			>
				<Box position="relative" display="inline-block">
					{/* Background glow effect */}
					<Box
						position="absolute"
						top="50%"
						left="50%"
						transform="translate(-50%, -50%)"
						width="40px"
						height="40px"
						borderRadius="full"
						filter="blur(15px)"
						bg="linear-gradient(45deg, rgba(0,204,133,0.3), rgba(0,121,185,0.3))"
						opacity="0.6"
						zIndex="-1"
					/>
					{/* Semi-transparent background circle for better visibility */}
					<Box
						position="absolute"
						top="50%"
						left="50%"
						transform="translate(-50%, -50%)"
						width="32px"
						height="32px"
						borderRadius="full"
						bg={logoBgColor}
						zIndex="1"
					/>
					<Image
						alt="logo"
						w={8}
						src="/icon.svg"
						cursor="pointer"
						onClick={() => window.location.href = "/"}
						transition="transform 0.3s ease"
						_hover={{ transform: "scale(1.1)" }}
						position="relative"
						zIndex="2"
						filter={logoFilter}
						style={{
							filter: logoDropShadow
						}}
					/>
				</Box>
			</Flex>

			{/* Navigation for authenticated users */}
			{user && (
				<Flex
					direction={isMobile ? "row" : "column"}
					align="center"
					position="fixed"
					left={isMobile ? "50%" : 4}
					bottom={isMobile ? 4 : "auto"}
					top={isMobile ? "auto" : "50%"}
					transform={isMobile ? "translateX(-50%)" : "translateY(-50%)"}
					gap={{ base: 3, xs: 4, sm: 5, lg: 6 }}
					className={isMobile ? "glass-navbar" : ""}
					px={isMobile ? { base: 2, xs: 3, sm: 4 } : 0}
					py={isMobile ? { base: 2, xs: 2.5, sm: 3 } : 0}
					borderRadius={isMobile ? "full" : "none"}
					zIndex={100}
				>
					{/* Home Button */}
					<Button
						as={RouterLink}
						to="/"
						variant="ghost"
						size={{ base: "md", sm: "lg" }}
						minH={{ base: "44px", sm: "48px" }} // Touch-friendly minimum height
						minW={{ base: "44px", sm: "48px" }} // Touch-friendly minimum width
						bg={isActive("/") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<House
							size={isVerySmall ? 24 : isSmallMobile ? 26 : 28}
							weight={isActive("/") ? "fill" : "bold"}
							color={isActive("/") ? "#00CC85" : navIconColor}
						/>
						{isActive("/") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Search Button */}
					<Button
						as={RouterLink}
						to="/search"
						variant="ghost"
						size={{ base: "md", sm: "lg" }}
						minH={{ base: "44px", sm: "48px" }}
						minW={{ base: "44px", sm: "48px" }}
						bg={isActive("/search") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<MagnifyingGlass
							size={isVerySmall ? 24 : isSmallMobile ? 26 : 28}
							weight={isActive("/search") ? "fill" : "bold"}
							color={isActive("/search") ? "#00CC85" : navIconColor}
						/>
						{isActive("/search") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Profile Button */}
					<Button
						as={RouterLink}
						to={`/${user.username}`}
						variant="ghost"
						size={{ base: "md", sm: "lg" }}
						minH={{ base: "44px", sm: "48px" }}
						minW={{ base: "44px", sm: "48px" }}
						bg={isActive("/:username") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<User
							size={isVerySmall ? 24 : isSmallMobile ? 26 : 28}
							weight="fill"
							color={isActive("/:username") ? "#00CC85" : "#616161"}
						/>
						{isActive("/:username") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Notifications Button */}
					<Button
						as={RouterLink}
						to="/notifications"
						variant="ghost"
						size={{ base: "md", sm: "lg" }}
						minH={{ base: "44px", sm: "48px" }}
						minW={{ base: "44px", sm: "48px" }}
						bg={isActive("/notifications") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<Bell
							size={isVerySmall ? 24 : isSmallMobile ? 26 : 28}
							weight={isActive("/notifications") ? "fill" : "bold"}
							color={isActive("/notifications") ? "#00CC85" : "#616161"}
						/>
						{isActive("/notifications") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>

					{/* Chat Button */}
					<Button
						as={RouterLink}
						to="/chat"
						variant="ghost"
						size={{ base: "md", sm: "lg" }}
						minH={{ base: "44px", sm: "48px" }}
						minW={{ base: "44px", sm: "48px" }}
						bg={isActive("/chat") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<Chat
							size={isVerySmall ? 24 : isSmallMobile ? 26 : 28}
							weight="fill"
							color={isActive("/chat") ? "#00CC85" : "#616161"}
						/>
						{isActive("/chat") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>




					{/* Settings Button */}
					<Button
						as={RouterLink}
						to="/settings"
						variant="ghost"
						size="lg"
						bg={isActive("/settings") ? "rgba(0, 204, 133, 0.1)" : "transparent"}
						_hover={{ bg: "rgba(0, 204, 133, 0.1)" }}
						position="relative"
					>
						<Gear
							size={28}
							weight={isActive("/settings") ? "fill" : "bold"}
							color={isActive("/settings") ? "#00CC85" : "#616161"}
						/>
						{isActive("/settings") && (
							<Box className="nav-active-indicator" />
						)}
					</Button>
				</Flex>
			)}
		</Flex>
	);
};

export default Header;
