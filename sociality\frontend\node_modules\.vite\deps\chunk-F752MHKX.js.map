{"version": 3, "sources": ["../../@chakra-ui/react-children-utils/dist/index.mjs"], "sourcesContent": ["// src/index.ts\nimport { Children, isValidElement } from \"react\";\nfunction getValidChildren(children) {\n  return Children.toArray(children).filter(\n    (child) => isValidElement(child)\n  );\n}\nexport {\n  getValidChildren\n};\n"], "mappings": ";;;;;;;;AACA,mBAAyC;AACzC,SAAS,iBAAiB,UAAU;AAClC,SAAO,sBAAS,QAAQ,QAAQ,EAAE;AAAA,IAChC,CAAC,cAAU,6BAAe,KAAK;AAAA,EACjC;AACF;", "names": []}