{"version": 3, "sources": ["../../@chakra-ui/portal/src/portal-manager.tsx", "../../@chakra-ui/portal/src/portal.tsx"], "sourcesContent": ["import { createContext } from \"@chakra-ui/react-context\"\n\ninterface PortalManagerContext {\n  zIndex?: number\n}\n\nconst [PortalManagerContextProvider, usePortalManager] =\n  createContext<PortalManagerContext | null>({\n    strict: false,\n    name: \"PortalManagerContext\",\n  })\n\nexport { usePortalManager }\n\nexport interface PortalManagerProps {\n  children?: React.ReactNode\n  /**\n   * [Z-Index war] If your has multiple elements\n   * with z-index clashing, you might need to apply a z-index to the Portal manager\n   */\n  zIndex?: number\n}\n\nexport function PortalManager(props: PortalManagerProps) {\n  const { children, zIndex } = props\n  return (\n    <PortalManagerContextProvider value={{ zIndex }}>\n      {children}\n    </PortalManagerContextProvider>\n  )\n}\n\nPortalManager.displayName = \"PortalManager\"\n", "import { useSafeLayoutEffect } from \"@chakra-ui/react-use-safe-layout-effect\"\nimport { createContext } from \"@chakra-ui/react-context\"\nimport { createPortal } from \"react-dom\"\nimport { usePortalManager } from \"./portal-manager\"\nimport { useEffect, useMemo, useRef, useState } from \"react\"\n\ntype PortalContext = HTMLDivElement | null\n\nconst [PortalContextProvider, usePortalContext] = createContext<PortalContext>({\n  strict: false,\n  name: \"PortalContext\",\n})\n\nconst PORTAL_CLASSNAME = \"chakra-portal\"\nconst PORTAL_SELECTOR = `.chakra-portal`\n\nconst Container = (props: React.PropsWithChildren<{ zIndex: number }>) => (\n  <div\n    className=\"chakra-portal-zIndex\"\n    style={{\n      position: \"absolute\",\n      zIndex: props.zIndex,\n      top: 0,\n      left: 0,\n      right: 0,\n      // NB: Don't add `bottom: 0`, it makes the entire app unusable\n      // @see https://github.com/chakra-ui/chakra-ui/issues/3201\n    }}\n  >\n    {props.children}\n  </div>\n)\n\n/**\n * Portal that uses `document.body` as container\n */\nconst DefaultPortal = (\n  props: React.PropsWithChildren<{ appendToParentPortal?: boolean }>,\n) => {\n  const { appendToParentPortal, children } = props\n\n  const [tempNode, setTempNode] = useState<HTMLElement | null>(null)\n  const portal = useRef<HTMLDivElement | null>(null)\n\n  const [, forceUpdate] = useState({})\n  useEffect(() => forceUpdate({}), [])\n\n  const parentPortal = usePortalContext()\n  const manager = usePortalManager()\n\n  useSafeLayoutEffect(() => {\n    if (!tempNode) return\n\n    const doc = tempNode.ownerDocument\n    const host = appendToParentPortal ? parentPortal ?? doc.body : doc.body\n\n    if (!host) return\n\n    portal.current = doc.createElement(\"div\")\n    portal.current.className = PORTAL_CLASSNAME\n\n    host.appendChild(portal.current)\n    forceUpdate({})\n\n    const portalNode = portal.current\n    return () => {\n      if (host.contains(portalNode)) {\n        host.removeChild(portalNode)\n      }\n    }\n  }, [tempNode])\n\n  const _children = manager?.zIndex ? (\n    <Container zIndex={manager?.zIndex}>{children}</Container>\n  ) : (\n    children\n  )\n\n  return portal.current ? (\n    createPortal(\n      <PortalContextProvider value={portal.current}>\n        {_children}\n      </PortalContextProvider>,\n      portal.current,\n    )\n  ) : (\n    <span\n      ref={(el) => {\n        if (el) setTempNode(el)\n      }}\n    />\n  )\n}\n\ninterface ContainerPortalProps extends React.PropsWithChildren<{}> {\n  containerRef: React.RefObject<HTMLElement | null>\n  /**\n   * @default false\n   */\n  appendToParentPortal?: boolean\n}\n\n/**\n * Portal that uses a custom container\n */\nconst ContainerPortal = (props: ContainerPortalProps) => {\n  const { children, containerRef, appendToParentPortal } = props\n  const containerEl = containerRef.current\n  const host =\n    containerEl ?? (typeof window !== \"undefined\" ? document.body : undefined)\n\n  const portal = useMemo(() => {\n    const node = containerEl?.ownerDocument.createElement(\"div\")\n    if (node) node.className = PORTAL_CLASSNAME\n    return node\n  }, [containerEl])\n\n  const [, forceUpdate] = useState({})\n  useSafeLayoutEffect(() => forceUpdate({}), [])\n\n  useSafeLayoutEffect(() => {\n    if (!portal || !host) return\n    host.appendChild(portal)\n    return () => {\n      host.removeChild(portal)\n    }\n  }, [portal, host])\n\n  if (host && portal) {\n    return createPortal(\n      <PortalContextProvider value={appendToParentPortal ? portal : null}>\n        {children}\n      </PortalContextProvider>,\n      portal,\n    )\n  }\n\n  return null\n}\n\nexport interface PortalProps {\n  /**\n   * The `ref` to the component where the portal will be attached to.\n   */\n  containerRef?: React.RefObject<HTMLElement | null>\n  /**\n   * The content or node you'll like to portal\n   */\n  children: React.ReactNode\n  /**\n   * If `true`, the portal will check if it is within a parent portal\n   * and append itself to the parent's portal node.\n   * This provides nesting for portals.\n   *\n   * If `false`, the portal will always append to `document.body`\n   * regardless of nesting. It is used to opt out of portal nesting.\n   *\n   * @default true\n   */\n  appendToParentPortal?: boolean\n}\n\n/**\n * Portal\n *\n * Declarative component used to render children into a DOM node\n * that exists outside the DOM hierarchy of the parent component.\n *\n * @see Docs https://chakra-ui.com/portal\n */\n\nexport function Portal(props: PortalProps) {\n  const portalProps: PortalProps = {\n    appendToParentPortal: true,\n    ...props,\n  }\n\n  const { containerRef, ...rest } = portalProps\n  return containerRef ? (\n    <ContainerPortal containerRef={containerRef} {...rest} />\n  ) : (\n    <DefaultPortal {...rest} />\n  )\n}\n\nPortal.className = PORTAL_CLASSNAME\nPortal.selector = PORTAL_SELECTOR\n\nPortal.displayName = \"Portal\"\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA0BI,yBAAA;AApBJ,IAAM,CAAC,8BAA8B,gBAAgB,IACnD,cAA2C;EACzC,QAAQ;EACR,MAAM;AACR,CAAC;AAaI,SAAS,cAAc,OAA2B;AACvD,QAAM,EAAE,UAAU,OAAO,IAAI;AAC7B,aACE,wBAAC,8BAAA,EAA6B,OAAO,EAAE,OAAO,GAC3C,SAAA,CACH;AAEJ;AAEA,cAAc,cAAc;;;AC9B5B,uBAA6B;AAE7B,mBAAqD;AAanD,IAAAA,sBAAA;AATF,IAAM,CAAC,uBAAuB,gBAAgB,IAAI,cAA6B;EAC7E,QAAQ;EACR,MAAM;AACR,CAAC;AAED,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AAExB,IAAM,YAAY,CAAC,cACjB;EAAC;EAAA;IACC,WAAU;IACV,OAAO;MACL,UAAU;MACV,QAAQ,MAAM;MACd,KAAK;MACL,MAAM;MACN,OAAO;;;IAGT;IAEC,UAAA,MAAM;EAAA;AACT;AAMF,IAAM,gBAAgB,CACpB,UACG;AACH,QAAM,EAAE,sBAAsB,SAAS,IAAI;AAE3C,QAAM,CAAC,UAAU,WAAW,QAAI,uBAA6B,IAAI;AACjE,QAAM,aAAS,qBAA8B,IAAI;AAEjD,QAAM,CAAC,EAAE,WAAW,QAAI,uBAAS,CAAC,CAAC;AACnC,8BAAU,MAAM,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;AAEnC,QAAM,eAAe,iBAAiB;AACtC,QAAM,UAAU,iBAAiB;AAEjC,sBAAoB,MAAM;AACxB,QAAI,CAAC;AAAU;AAEf,UAAM,MAAM,SAAS;AACrB,UAAM,OAAO,uBAAuB,gBAAA,OAAA,eAAgB,IAAI,OAAO,IAAI;AAEnE,QAAI,CAAC;AAAM;AAEX,WAAO,UAAU,IAAI,cAAc,KAAK;AACxC,WAAO,QAAQ,YAAY;AAE3B,SAAK,YAAY,OAAO,OAAO;AAC/B,gBAAY,CAAC,CAAC;AAEd,UAAM,aAAa,OAAO;AAC1B,WAAO,MAAM;AACX,UAAI,KAAK,SAAS,UAAU,GAAG;AAC7B,aAAK,YAAY,UAAU;MAC7B;IACF;EACF,GAAG,CAAC,QAAQ,CAAC;AAEb,QAAM,aAAY,WAAA,OAAA,SAAA,QAAS,cACzB,yBAAC,WAAA,EAAU,QAAQ,WAAA,OAAA,SAAA,QAAS,QAAS,SAAA,CAAS,IAE9C;AAGF,SAAO,OAAO,cACZ;QACE,yBAAC,uBAAA,EAAsB,OAAO,OAAO,SAClC,UAAA,UAAA,CACH;IACA,OAAO;EACT,QAEA;IAAC;IAAA;MACC,KAAK,CAAC,OAAO;AACX,YAAI;AAAI,sBAAY,EAAE;MACxB;IAAA;EACF;AAEJ;AAaA,IAAM,kBAAkB,CAAC,UAAgC;AACvD,QAAM,EAAE,UAAU,cAAc,qBAAqB,IAAI;AACzD,QAAM,cAAc,aAAa;AACjC,QAAM,OACJ,eAAA,OAAA,cAAgB,OAAO,WAAW,cAAc,SAAS,OAAO;AAElE,QAAM,aAAS,sBAAQ,MAAM;AAC3B,UAAM,OAAO,eAAA,OAAA,SAAA,YAAa,cAAc,cAAc,KAAA;AACtD,QAAI;AAAM,WAAK,YAAY;AAC3B,WAAO;EACT,GAAG,CAAC,WAAW,CAAC;AAEhB,QAAM,CAAC,EAAE,WAAW,QAAI,uBAAS,CAAC,CAAC;AACnC,sBAAoB,MAAM,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;AAE7C,sBAAoB,MAAM;AACxB,QAAI,CAAC,UAAU,CAAC;AAAM;AACtB,SAAK,YAAY,MAAM;AACvB,WAAO,MAAM;AACX,WAAK,YAAY,MAAM;IACzB;EACF,GAAG,CAAC,QAAQ,IAAI,CAAC;AAEjB,MAAI,QAAQ,QAAQ;AAClB,eAAO;UACL,yBAAC,uBAAA,EAAsB,OAAO,uBAAuB,SAAS,MAC3D,SAAA,CACH;MACA;IACF;EACF;AAEA,SAAO;AACT;AAiCO,SAAS,OAAO,OAAoB;AACzC,QAAM,cAA2B;IAC/B,sBAAsB;IACtB,GAAG;EACL;AAEA,QAAM,EAAE,cAAc,GAAG,KAAK,IAAI;AAClC,SAAO,mBACL,yBAAC,iBAAA,EAAgB,cAA6B,GAAG,KAAA,CAAM,QAEvD,yBAAC,eAAA,EAAe,GAAG,KAAA,CAAM;AAE7B;AAEA,OAAO,YAAY;AACnB,OAAO,WAAW;AAElB,OAAO,cAAc;", "names": ["import_jsx_runtime"]}