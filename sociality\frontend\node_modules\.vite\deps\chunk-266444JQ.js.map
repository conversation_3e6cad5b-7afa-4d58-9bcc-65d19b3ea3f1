{"version": 3, "sources": ["../../@chakra-ui/image/src/native-image.tsx", "../../@chakra-ui/image/src/use-image.ts", "../../@chakra-ui/image/src/image.tsx", "../../utilities/object-utils/src/omit.ts", "../../@chakra-ui/image/src/img.tsx"], "sourcesContent": ["import { PropsOf, forwardRef } from \"@chakra-ui/system\"\n\nexport interface NativeImageOptions {\n  /**\n   * The native HTML `width` attribute to the passed to the `img`\n   */\n  htmlWidth?: string | number\n  /**\n   * The native HTML `height` attribute to the passed to the `img`\n   */\n  htmlHeight?: string | number\n}\n\ninterface NativeImageProps extends PropsOf<\"img\">, NativeImageOptions {}\n\nexport const NativeImage = forwardRef(function NativeImage(\n  props: NativeImageProps,\n  ref: React.Ref<any>,\n) {\n  const { htmlWidth, htmlHeight, alt, ...rest } = props\n  return (\n    <img width={htmlWidth} height={htmlHeight} ref={ref} alt={alt} {...rest} />\n  )\n})\n\nNativeImage.displayName = \"NativeImage\"\n", "import { useSafeLayoutEffect } from \"@chakra-ui/react-use-safe-layout-effect\"\nimport { useCallback, useEffect, useRef, useState } from \"react\"\n\ntype NativeImageProps = React.ImgHTMLAttributes<HTMLImageElement>\n\nexport interface UseImageProps {\n  /**\n   * The image `src` attribute\n   */\n  src?: string\n  /**\n   * The image `srcset` attribute\n   */\n  srcSet?: string\n  /**\n   * The image `sizes` attribute\n   */\n  sizes?: string\n  /**\n   * A callback for when the image `src` has been loaded\n   */\n  onLoad?: NativeImageProps[\"onLoad\"]\n  /**\n   * A callback for when there was an error loading the image `src`\n   */\n  onError?: NativeImageProps[\"onError\"]\n  /**\n   * If `true`, opt out of the `fallbackSrc` logic and use as `img`\n   *\n   * @default false\n   */\n  ignoreFallback?: boolean\n  /**\n   * The key used to set the crossOrigin on the HTMLImageElement into which the image will be loaded.\n   * This tells the browser to request cross-origin access when trying to download the image data.\n   */\n  crossOrigin?: NativeImageProps[\"crossOrigin\"]\n  loading?: NativeImageProps[\"loading\"]\n}\n\ntype Status = \"loading\" | \"failed\" | \"pending\" | \"loaded\"\n\nexport type FallbackStrategy = \"onError\" | \"beforeLoadOrError\"\n\ntype ImageEvent = React.SyntheticEvent<HTMLImageElement, Event>\n\n/**\n * React hook that loads an image in the browser,\n * and lets us know the `status` so we can show image\n * fallback if it is still `pending`\n *\n * @returns the status of the image loading progress\n *\n * @example\n *\n * ```jsx\n * function App(){\n *   const status = useImage({ src: \"image.png\" })\n *   return status === \"loaded\" ? <img src=\"image.png\" /> : <Placeholder />\n * }\n * ```\n */\nexport function useImage(props: UseImageProps) {\n  const {\n    loading,\n    src,\n    srcSet,\n    onLoad,\n    onError,\n    crossOrigin,\n    sizes,\n    ignoreFallback,\n  } = props\n\n  const [status, setStatus] = useState<Status>(\"pending\")\n\n  useEffect(() => {\n    setStatus(src ? \"loading\" : \"pending\")\n  }, [src])\n\n  const imageRef = useRef<HTMLImageElement | null>()\n\n  const load = useCallback(() => {\n    if (!src) return\n\n    flush()\n\n    const img = new Image()\n    img.src = src\n    if (crossOrigin) img.crossOrigin = crossOrigin\n    if (srcSet) img.srcset = srcSet\n    if (sizes) img.sizes = sizes\n    if (loading) img.loading = loading\n\n    img.onload = (event) => {\n      flush()\n      setStatus(\"loaded\")\n      onLoad?.(event as unknown as ImageEvent)\n    }\n    img.onerror = (error) => {\n      flush()\n      setStatus(\"failed\")\n      onError?.(error as any)\n    }\n\n    imageRef.current = img\n  }, [src, crossOrigin, srcSet, sizes, onLoad, onError, loading])\n\n  const flush = () => {\n    if (imageRef.current) {\n      imageRef.current.onload = null\n      imageRef.current.onerror = null\n      imageRef.current = null\n    }\n  }\n\n  useSafeLayoutEffect(() => {\n    /**\n     * If user opts out of the fallback/placeholder\n     * logic, let's bail out.\n     */\n    if (ignoreFallback) return undefined\n\n    if (status === \"loading\") {\n      load()\n    }\n    return () => {\n      flush()\n    }\n  }, [status, load, ignoreFallback])\n\n  /**\n   * If user opts out of the fallback/placeholder\n   * logic, let's just return 'loaded'\n   */\n  return ignoreFallback ? \"loaded\" : status\n}\n\nexport const shouldShowFallbackImage = (\n  status: Status,\n  fallbackStrategy: FallbackStrategy,\n) =>\n  (status !== \"loaded\" && fallbackStrategy === \"beforeLoadOrError\") ||\n  (status === \"failed\" && fallbackStrategy === \"onError\")\n\nexport type UseImageReturn = ReturnType<typeof useImage>\n", "import {\n  chakra,\n  SystemProps,\n  forwardRef,\n  HTMLChakraProps,\n} from \"@chakra-ui/system\"\nimport { omit } from \"@chakra-ui/object-utils\"\nimport {\n  FallbackStrategy,\n  shouldShowFallbackImage,\n  useImage,\n  UseImageProps,\n} from \"./use-image\"\nimport { NativeImage, NativeImageOptions } from \"./native-image\"\n\ninterface ImageOptions extends NativeImageOptions {\n  /**\n   * Fallback image `src` to show if image is loading or image fails.\n   *\n   * Note 🚨: We recommend you use a local image\n   */\n  fallbackSrc?: string\n  /**\n   * Fallback element to show if image is loading or image fails.\n   * @type React.ReactElement\n   */\n  fallback?: React.ReactElement\n  /**\n   * Defines loading strategy\n   */\n  loading?: \"eager\" | \"lazy\"\n  /**\n   * How the image to fit within its bounds.\n   * It maps to css `object-fit` property.\n   * @type SystemProps[\"objectFit\"]\n   */\n  fit?: SystemProps[\"objectFit\"]\n  /**\n   * How to align the image within its bounds.\n   * It maps to css `object-position` property.\n   * @type SystemProps[\"objectPosition\"]\n   */\n  align?: SystemProps[\"objectPosition\"]\n  /**\n   * If `true`, opt out of the `fallbackSrc` logic and use as `img`\n   *\n   * @default false\n   */\n  ignoreFallback?: boolean\n\n  /**\n   * - beforeLoadOrError(default): loads the fallbackImage while loading the src\n   * - onError: loads the fallbackImage only if there is an error fetching the src\n   *\n   * @default \"beforeLoadOrError\"\n   * @see Issue https://github.com/chakra-ui/chakra-ui/issues/5581\n   */\n  fallbackStrategy?: FallbackStrategy\n  /**\n   * Defining which referrer is sent when fetching the resource.\n   * @type React.HTMLAttributeReferrerPolicy\n   */\n  referrerPolicy?: React.HTMLAttributeReferrerPolicy\n}\n\nexport interface ImageProps\n  extends UseImageProps,\n    Omit<HTMLChakraProps<\"img\">, keyof UseImageProps>,\n    ImageOptions {}\n\n/**\n * React component that renders an image with support\n * for fallbacks\n *\n * @see Docs https://chakra-ui.com/image\n */\nexport const Image = forwardRef<ImageProps, \"img\">(function Image(props, ref) {\n  const {\n    fallbackSrc,\n    fallback,\n    src,\n    srcSet,\n    align,\n    fit,\n    loading,\n    ignoreFallback,\n    crossOrigin,\n    fallbackStrategy = \"beforeLoadOrError\",\n    referrerPolicy,\n    ...rest\n  } = props\n\n  const providedFallback = fallbackSrc !== undefined || fallback !== undefined\n  /**\n   * Defer to native `img` tag if `loading` prop is passed\n   * @see https://github.com/chakra-ui/chakra-ui/issues/1027\n   *\n   * shouldIgnoreFallbackImage determines if we have the possibility to render a fallback image\n   */\n  const shouldIgnoreFallbackImage =\n    loading != null ||\n    // use can opt out of fallback image\n    ignoreFallback ||\n    // if the user doesn't provide any kind of fallback we should ignore it\n    !providedFallback\n\n  /**\n   * returns `loaded` if fallback is ignored\n   */\n  const status = useImage({\n    ...props,\n    crossOrigin,\n    ignoreFallback: shouldIgnoreFallbackImage,\n  })\n\n  const showFallbackImage = shouldShowFallbackImage(status, fallbackStrategy)\n\n  const shared = {\n    ref,\n    objectFit: fit,\n    objectPosition: align,\n    ...(shouldIgnoreFallbackImage ? rest : omit(rest, [\"onError\", \"onLoad\"])),\n  }\n\n  if (showFallbackImage) {\n    /**\n     * If user passed a custom fallback component,\n     * let's render it here.\n     */\n    if (fallback) return fallback\n\n    return (\n      <chakra.img\n        as={NativeImage}\n        className=\"chakra-image__placeholder\"\n        src={fallbackSrc}\n        {...shared}\n      />\n    )\n  }\n\n  return (\n    <chakra.img\n      as={NativeImage}\n      src={src}\n      srcSet={srcSet}\n      crossOrigin={crossOrigin}\n      loading={loading}\n      referrerPolicy={referrerPolicy}\n      className=\"chakra-image\"\n      {...shared}\n    />\n  )\n})\n\nImage.displayName = \"Image\"\n", "export function omit<T extends Record<string, any>, <PERSON> extends keyof T>(\n  object: T,\n  keysToOmit: K[] = [],\n) {\n  const clone: Record<string, unknown> = Object.assign({}, object)\n  for (const key of keysToOmit) {\n    if (key in clone) {\n      delete clone[key as string]\n    }\n  }\n  return clone as Omit<T, K>\n}\n", "import { HTMLChakraProps, chakra, forwardRef } from \"@chakra-ui/system\"\n\nimport { NativeImageOptions, NativeImage } from \"./native-image\"\n\nexport interface ImgProps extends HTMLChakraProps<\"img\">, NativeImageOptions {}\n\n/**\n * Fallback component for most SSR users who want to use the native `img` with\n * support for chakra props\n */\nexport const Img = forwardRef<ImgProps, \"img\">((props, ref) => (\n  <chakra.img ref={ref} as={NativeImage} className=\"chakra-image\" {...props} />\n))\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAqBI,yBAAA;AANG,IAAM,cAAc,WAAW,SAASA,aAC7C,OACA,KACA;AACA,QAAM,EAAE,WAAW,YAAY,KAAK,GAAG,KAAK,IAAI;AAChD,aACE,wBAAC,OAAA,EAAI,OAAO,WAAW,QAAQ,YAAY,KAAU,KAAW,GAAG,KAAA,CAAM;AAE7E,CAAC;AAED,YAAY,cAAc;;;ACxB1B,mBAAyD;AA6DlD,SAAS,SAAS,OAAsB;AAC7C,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,IAAI;AAEJ,QAAM,CAAC,QAAQ,SAAS,QAAI,uBAAiB,SAAS;AAEtD,8BAAU,MAAM;AACd,cAAU,MAAM,YAAY,SAAS;EACvC,GAAG,CAAC,GAAG,CAAC;AAER,QAAM,eAAW,qBAAgC;AAEjD,QAAM,WAAO,0BAAY,MAAM;AAC7B,QAAI,CAAC;AAAK;AAEV,UAAM;AAEN,UAAM,MAAM,IAAI,MAAM;AACtB,QAAI,MAAM;AACV,QAAI;AAAa,UAAI,cAAc;AACnC,QAAI;AAAQ,UAAI,SAAS;AACzB,QAAI;AAAO,UAAI,QAAQ;AACvB,QAAI;AAAS,UAAI,UAAU;AAE3B,QAAI,SAAS,CAAC,UAAU;AACtB,YAAM;AACN,gBAAU,QAAQ;AAClB,gBAAA,OAAA,SAAA,OAAS,KAAA;IACX;AACA,QAAI,UAAU,CAAC,UAAU;AACvB,YAAM;AACN,gBAAU,QAAQ;AAClB,iBAAA,OAAA,SAAA,QAAU,KAAA;IACZ;AAEA,aAAS,UAAU;EACrB,GAAG,CAAC,KAAK,aAAa,QAAQ,OAAO,QAAQ,SAAS,OAAO,CAAC;AAE9D,QAAM,QAAQ,MAAM;AAClB,QAAI,SAAS,SAAS;AACpB,eAAS,QAAQ,SAAS;AAC1B,eAAS,QAAQ,UAAU;AAC3B,eAAS,UAAU;IACrB;EACF;AAEA,sBAAoB,MAAM;AAKxB,QAAI;AAAgB,aAAO;AAE3B,QAAI,WAAW,WAAW;AACxB,WAAK;IACP;AACA,WAAO,MAAM;AACX,YAAM;IACR;EACF,GAAG,CAAC,QAAQ,MAAM,cAAc,CAAC;AAMjC,SAAO,iBAAiB,WAAW;AACrC;AAEO,IAAM,0BAA0B,CACrC,QACA,qBAEC,WAAW,YAAY,qBAAqB,uBAC5C,WAAW,YAAY,qBAAqB;;;ACXzC,IAAAC,sBAAA;ACpIC,SAAS,KACd,QACA,aAAkB,CAAC,GACnB;AACA,QAAM,QAAiC,OAAO,OAAO,CAAC,GAAG,MAAM;AAC/D,aAAW,OAAO,YAAY;AAC5B,QAAI,OAAO,OAAO;AAChB,aAAO,MAAM,GAAa;IAC5B;EACF;AACA,SAAO;AACT;ADiEO,IAAMC,SAAQ,WAA8B,SAASA,QAAM,OAAO,KAAK;AAC5E,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,mBAAmB;IACnB;IACA,GAAG;EACL,IAAI;AAEJ,QAAM,mBAAmB,gBAAgB,UAAa,aAAa;AAOnE,QAAM,4BACJ,WAAW;EAEX;EAEA,CAAC;AAKH,QAAM,SAAS,SAAS;IACtB,GAAG;IACH;IACA,gBAAgB;EAClB,CAAC;AAED,QAAM,oBAAoB,wBAAwB,QAAQ,gBAAgB;AAE1E,QAAM,SAAS;IACb;IACA,WAAW;IACX,gBAAgB;IAChB,GAAI,4BAA4B,OAAO,KAAK,MAAM,CAAC,WAAW,QAAQ,CAAC;EACzE;AAEA,MAAI,mBAAmB;AAKrB,QAAI;AAAU,aAAO;AAErB,eACE;MAAC,OAAO;MAAP;QACC,IAAI;QACJ,WAAU;QACV,KAAK;QACJ,GAAG;MAAA;IACN;EAEJ;AAEA,aACE;IAAC,OAAO;IAAP;MACC,IAAI;MACJ;MACA;MACA;MACA;MACA;MACA,WAAU;MACT,GAAG;IAAA;EACN;AAEJ,CAAC;AAEDA,OAAM,cAAc;;;AEhJlB,IAAAC,sBAAA;AADK,IAAM,MAAM,WAA4B,CAAC,OAAO,YACrD,yBAAC,OAAO,KAAP,EAAW,KAAU,IAAI,aAAa,WAAU,gBAAgB,GAAG,MAAA,CAAO,CAC5E;", "names": ["NativeImage", "import_jsx_runtime", "Image", "import_jsx_runtime"]}