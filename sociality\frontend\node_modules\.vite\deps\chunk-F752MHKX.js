import {
  require_react
} from "./chunk-LXGCQ6UQ.js";
import {
  __toESM
} from "./chunk-ROME4SDB.js";

// node_modules/@chakra-ui/react-children-utils/dist/index.mjs
var import_react = __toESM(require_react(), 1);
function getValidChildren(children) {
  return import_react.Children.toArray(children).filter(
    (child) => (0, import_react.isValidElement)(child)
  );
}

export {
  getValidChildren
};
//# sourceMappingURL=chunk-F752MHKX.js.map
