{"version": 3, "sources": ["../../@chakra-ui/menu/src/use-shortcut.ts", "../../@chakra-ui/menu/src/get-next-item-from-search.ts", "../../@chakra-ui/clickable/src/use-event-listeners.ts", "../../@chakra-ui/react-use-merge-refs/src/index.ts", "../../@chakra-ui/clickable/src/use-clickable.ts", "../../@chakra-ui/descendant/src/utils.ts", "../../@chakra-ui/descendant/src/descendant.ts", "../../@chakra-ui/descendant/src/use-descendant.ts", "../../@chakra-ui/dom-utils/dist/chunk-3XANSPY5.mjs", "../../@chakra-ui/dom-utils/dist/chunk-ROURZMX4.mjs", "../../@chakra-ui/dom-utils/dist/index.mjs", "../../@chakra-ui/react-use-event-listener/src/index.ts", "../../@chakra-ui/react-use-focus-effect/src/index.ts", "../../@chakra-ui/popper/src/utils.ts", "../../@chakra-ui/popper/src/modifiers.ts", "../../@chakra-ui/popper/src/popper.placement.ts", "../../@chakra-ui/popper/src/use-popper.ts", "../../@chakra-ui/react-use-disclosure/src/index.ts", "../../@chakra-ui/react-use-outside-click/src/index.ts", "../../@chakra-ui/react-use-animation-state/src/index.ts", "../../@chakra-ui/react-use-controllable-state/src/index.ts", "../../@chakra-ui/lazy-utils/dist/index.mjs", "../../@chakra-ui/menu/src/use-menu.ts", "../../@chakra-ui/menu/src/menu.tsx", "../../@chakra-ui/menu/src/menu-command.tsx", "../../@chakra-ui/menu/src/styled-menu-item.tsx", "../../@chakra-ui/menu/src/menu-icon.tsx", "../../@chakra-ui/menu/src/menu-item.tsx", "../../@chakra-ui/menu/src/menu-list.tsx", "../../@chakra-ui/menu/src/menu-group.tsx", "../../@chakra-ui/menu/src/menu-option-group.tsx", "../../@chakra-ui/menu/src/menu-button.tsx", "../../@chakra-ui/menu/src/menu-divider.tsx", "../../@chakra-ui/menu/src/menu-item-option.tsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\"\n\n/**\n * Checks if the key pressed is a printable character\n * and can be used for shortcut navigation\n */\nfunction isPrintableCharacter(event: React.KeyboardEvent) {\n  const { key } = event\n  return key.length === 1 || (key.length > 1 && /[^a-zA-Z0-9]/.test(key))\n}\n\nexport interface UseShortcutProps {\n  timeout?: number\n  preventDefault?: (event: React.KeyboardEvent) => boolean\n}\n\n/**\n * React hook that provides an enhanced keydown handler,\n * that's used for key navigation within menus, select dropdowns.\n */\nexport function useShortcut(props: UseShortcutProps = {}) {\n  const { timeout = 300, preventDefault = () => true } = props\n\n  const [keys, setKeys] = useState<string[]>([])\n  const timeoutRef = useRef<any>()\n\n  const flush = () => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current)\n      timeoutRef.current = null\n    }\n  }\n\n  const clearKeysAfterDelay = () => {\n    flush()\n    timeoutRef.current = setTimeout(() => {\n      setKeys([])\n      timeoutRef.current = null\n    }, timeout)\n  }\n\n  useEffect(() => flush, [])\n\n  type Callback = (keysSoFar: string) => void\n\n  function onKeyDown(fn: Callback) {\n    return (event: React.KeyboardEvent) => {\n      if (event.key === \"Backspace\") {\n        const keysCopy = [...keys]\n        keysCopy.pop()\n        setKeys(keysCopy)\n        return\n      }\n\n      if (isPrintableCharacter(event)) {\n        const keysCopy = keys.concat(event.key)\n\n        if (preventDefault(event)) {\n          event.preventDefault()\n          event.stopPropagation()\n        }\n\n        setKeys(keysCopy)\n        fn(keysCopy.join(\"\"))\n\n        clearKeysAfterDelay()\n      }\n    }\n  }\n\n  return onKeyDown\n}\n", "/**\n * Gets the next item based on a search string\n *\n * @param items array of items\n * @param searchString the search string\n * @param itemToString resolves an item to string\n * @param currentItem the current selected item\n */\nexport function getNextItemFromSearch<T>(\n  items: T[],\n  searchString: string,\n  itemToString: (item: T) => string,\n  currentItem: T,\n): T | undefined {\n  if (searchString == null) {\n    return currentItem\n  }\n\n  // If current item doesn't exist, find the item that matches the search string\n  if (!currentItem) {\n    const foundItem = items.find((item) =>\n      itemToString(item).toLowerCase().startsWith(searchString.toLowerCase()),\n    )\n    return foundItem\n  }\n\n  // Filter items for ones that match the search string (case insensitive)\n  const matchingItems = items.filter((item) =>\n    itemToString(item).toLowerCase().startsWith(searchString.toLowerCase()),\n  )\n\n  // If there's a match, let's get the next item to select\n  if (matchingItems.length > 0) {\n    let nextIndex: number\n\n    // If the currentItem is in the available items, we move to the next available option\n    if (matchingItems.includes(currentItem)) {\n      const currentIndex = matchingItems.indexOf(currentItem)\n      nextIndex = currentIndex + 1\n      if (nextIndex === matchingItems.length) {\n        nextIndex = 0\n      }\n      return matchingItems[nextIndex]\n    }\n    // Else, we pick the first item in the available items\n    nextIndex = items.indexOf(matchingItems[0])\n    return items[nextIndex]\n  }\n\n  // a decent fallback to the currentItem\n  return currentItem\n}\n", "import { useCallback, useEffect, useRef } from \"react\"\n\ninterface EventListeners {\n  add<K extends keyof DocumentEventMap>(\n    el: EventTarget,\n    type: K,\n    listener: (this: Document, ev: DocumentEventMap[K]) => any,\n    options?: boolean | AddEventListenerOptions,\n  ): void\n  add(\n    el: EventTarget,\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | AddEventListenerOptions,\n  ): void\n  remove<K extends keyof DocumentEventMap>(\n    el: EventTarget,\n    type: K,\n    listener: (this: Document, ev: DocumentEventMap[K]) => any,\n    options?: boolean | EventListenerOptions,\n  ): void\n  remove(\n    el: EventTarget,\n    type: string,\n    listener: EventListenerOrEventListenerObject,\n    options?: boolean | EventListenerOptions,\n  ): void\n}\n\nexport function useEventListeners(): EventListeners {\n  const listeners = useRef(new Map())\n  const currentListeners = listeners.current\n\n  const add = useCallback((el: any, type: any, listener: any, options: any) => {\n    listeners.current.set(listener, { type, el, options })\n    el.addEventListener(type, listener, options)\n  }, [])\n\n  const remove = useCallback(\n    (el: any, type: any, listener: any, options: any) => {\n      el.removeEventListener(type, listener, options)\n      listeners.current.delete(listener)\n    },\n    [],\n  )\n\n  useEffect(\n    () => () => {\n      currentListeners.forEach((value, key) => {\n        remove(value.el, value.type, key, value.options)\n      })\n    },\n    [remove, currentListeners],\n  )\n\n  return { add, remove }\n}\n", "import { useMemo } from \"react\"\n\nexport type ReactRef<T> = React.RefCallback<T> | React.MutableRefObject<T>\n\nexport function assignRef<T = any>(\n  ref: ReactRef<T> | null | undefined,\n  value: T,\n) {\n  if (ref == null) return\n\n  if (typeof ref === \"function\") {\n    ref(value)\n    return\n  }\n\n  try {\n    ref.current = value\n  } catch (error) {\n    throw new Error(`Cannot assign value '${value}' to ref '${ref}'`)\n  }\n}\n\nexport function mergeRefs<T>(...refs: (ReactRef<T> | null | undefined)[]) {\n  return (node: T | null) => {\n    refs.forEach((ref) => {\n      assignRef(ref, node)\n    })\n  }\n}\n\nexport function useMergeRefs<T>(...refs: (ReactRef<T> | null | undefined)[]) {\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return useMemo(() => mergeRefs(...refs), refs)\n}\n", "import { dataAttr } from \"@chakra-ui/shared-utils\"\nimport { mergeRefs } from \"@chakra-ui/react-use-merge-refs\"\nimport { useEventListeners } from \"./use-event-listeners\"\nimport { useCallback, useState } from \"react\"\n\nexport interface UseClickableProps extends React.HTMLAttributes<HTMLElement> {\n  /**\n   * If `true`, the element will be disabled.\n   * It will set the `disabled` HTML attribute\n   *\n   * @default false\n   */\n  isDisabled?: boolean\n  /**\n   * If `true` and isDisabled, the element will\n   * have only `aria-disabled` set to `true`\n   *\n   * @default false\n   */\n  isFocusable?: boolean\n  /**\n   * Whether or not trigger click on pressing `Enter`.\n   *\n   * @default true\n   */\n  clickOnEnter?: boolean\n  /**\n   * Whether or not trigger click on pressing `Space`.\n   *\n   * @default true\n   */\n  clickOnSpace?: boolean\n  /**\n   * The ref for the element\n   */\n  ref?: React.Ref<HTMLElement>\n}\n\nfunction isValidElement(event: KeyboardEvent): boolean {\n  const element = event.target as HTMLElement\n  const { tagName, isContentEditable } = element\n  return (\n    tagName !== \"INPUT\" && tagName !== \"TEXTAREA\" && isContentEditable !== true\n  )\n}\n\n/**\n * useClickable implements all the interactions of a native `button`\n * component with support for making it focusable even if it is disabled.\n *\n * It can be used with both native button elements or other elements (like `div`).\n */\nexport function useClickable(props: UseClickableProps = {}) {\n  const {\n    ref: htmlRef,\n    isDisabled,\n    isFocusable,\n    clickOnEnter = true,\n    clickOnSpace = true,\n    onMouseDown,\n    onMouseUp,\n    onClick,\n    onKeyDown,\n    onKeyUp,\n    tabIndex: tabIndexProp,\n    onMouseOver,\n    onMouseLeave,\n    ...htmlProps\n  } = props\n  /**\n   * We'll use this to track if the element is a button element\n   */\n  const [isButton, setIsButton] = useState(true)\n\n  /**\n   * For custom button implementation, we'll use this to track when\n   * we mouse down on the button, to enable use style its \":active\" style\n   */\n  const [isPressed, setIsPressed] = useState(false)\n\n  const listeners = useEventListeners()\n\n  /**\n   * The ref callback that fires as soon as the dom node is ready\n   */\n  const refCallback = (node: any) => {\n    if (!node) return\n    if (node.tagName !== \"BUTTON\") {\n      setIsButton(false)\n    }\n  }\n\n  const tabIndex = isButton ? tabIndexProp : tabIndexProp || 0\n  const trulyDisabled = isDisabled && !isFocusable\n\n  const handleClick = useCallback(\n    (event: React.MouseEvent<HTMLElement>) => {\n      if (isDisabled) {\n        event.stopPropagation()\n        event.preventDefault()\n        return\n      }\n\n      const self = event.currentTarget as HTMLElement\n      self.focus()\n      onClick?.(event)\n    },\n    [isDisabled, onClick],\n  )\n\n  const onDocumentKeyUp = useCallback(\n    (e: KeyboardEvent) => {\n      if (isPressed && isValidElement(e)) {\n        e.preventDefault()\n        e.stopPropagation()\n\n        setIsPressed(false)\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        listeners.remove(document, \"keyup\", onDocumentKeyUp, false)\n      }\n    },\n    [isPressed, listeners],\n  )\n\n  const handleKeyDown = useCallback(\n    (event: React.KeyboardEvent<HTMLElement>) => {\n      onKeyDown?.(event)\n\n      if (isDisabled || event.defaultPrevented || event.metaKey) {\n        return\n      }\n\n      if (!isValidElement(event.nativeEvent) || isButton) return\n\n      const shouldClickOnEnter = clickOnEnter && event.key === \"Enter\"\n      const shouldClickOnSpace = clickOnSpace && event.key === \" \"\n\n      if (shouldClickOnSpace) {\n        event.preventDefault()\n        setIsPressed(true)\n      }\n\n      if (shouldClickOnEnter) {\n        event.preventDefault()\n        const self = event.currentTarget as HTMLElement\n        self.click()\n      }\n\n      listeners.add(document, \"keyup\", onDocumentKeyUp, false)\n    },\n    [\n      isDisabled,\n      isButton,\n      onKeyDown,\n      clickOnEnter,\n      clickOnSpace,\n      listeners,\n      onDocumentKeyUp,\n    ],\n  )\n\n  const handleKeyUp = useCallback(\n    (event: React.KeyboardEvent<HTMLElement>) => {\n      onKeyUp?.(event)\n\n      if (isDisabled || event.defaultPrevented || event.metaKey) return\n\n      if (!isValidElement(event.nativeEvent) || isButton) return\n\n      const shouldClickOnSpace = clickOnSpace && event.key === \" \"\n\n      if (shouldClickOnSpace) {\n        event.preventDefault()\n        setIsPressed(false)\n\n        const self = event.currentTarget as HTMLElement\n        self.click()\n      }\n    },\n    [clickOnSpace, isButton, isDisabled, onKeyUp],\n  )\n\n  const onDocumentMouseUp = useCallback(\n    (event: MouseEvent) => {\n      if (event.button !== 0) return\n      setIsPressed(false)\n      listeners.remove(document, \"mouseup\", onDocumentMouseUp, false)\n    },\n    [listeners],\n  )\n\n  const handleMouseDown = useCallback(\n    (event: React.MouseEvent<HTMLElement>) => {\n      if (event.button !== 0) return\n\n      if (isDisabled) {\n        event.stopPropagation()\n        event.preventDefault()\n        return\n      }\n\n      if (!isButton) {\n        setIsPressed(true)\n      }\n\n      const target = event.currentTarget as HTMLElement\n      target.focus({ preventScroll: true })\n\n      listeners.add(document, \"mouseup\", onDocumentMouseUp, false)\n\n      onMouseDown?.(event)\n    },\n    [isDisabled, isButton, onMouseDown, listeners, onDocumentMouseUp],\n  )\n\n  const handleMouseUp = useCallback(\n    (event: React.MouseEvent<HTMLElement>) => {\n      if (event.button !== 0) return\n\n      if (!isButton) {\n        setIsPressed(false)\n      }\n\n      onMouseUp?.(event)\n    },\n    [onMouseUp, isButton],\n  )\n\n  const handleMouseOver = useCallback(\n    (event: React.MouseEvent<HTMLElement>) => {\n      if (isDisabled) {\n        event.preventDefault()\n        return\n      }\n\n      onMouseOver?.(event)\n    },\n    [isDisabled, onMouseOver],\n  )\n\n  const handleMouseLeave = useCallback(\n    (event: React.MouseEvent<HTMLElement>) => {\n      if (isPressed) {\n        event.preventDefault()\n        setIsPressed(false)\n      }\n      onMouseLeave?.(event)\n    },\n    [isPressed, onMouseLeave],\n  )\n\n  const ref = mergeRefs(htmlRef, refCallback)\n\n  if (isButton) {\n    return {\n      ...htmlProps,\n      ref,\n      type: \"button\" as React.ButtonHTMLAttributes<any>[\"type\"],\n      \"aria-disabled\": trulyDisabled ? undefined : isDisabled,\n      disabled: trulyDisabled,\n      onClick: handleClick,\n      onMouseDown,\n      onMouseUp,\n      onKeyUp,\n      onKeyDown,\n      onMouseOver,\n      onMouseLeave,\n    }\n  }\n\n  return {\n    ...htmlProps,\n    ref,\n    role: \"button\",\n    \"data-active\": dataAttr(isPressed),\n    \"aria-disabled\": isDisabled ? (\"true\" as const) : undefined,\n    tabIndex: trulyDisabled ? undefined : tabIndex,\n    onClick: handleClick,\n    onMouseDown: handleMouseDown,\n    onMouseUp: handleMouseUp,\n    onKeyUp: handleKeyUp,\n    onKeyDown: handleKeyDown,\n    onMouseOver: handleMouseOver,\n    onMouseLeave: handleMouseLeave,\n  }\n}\n\nexport type UseClickableReturn = ReturnType<typeof useClickable>\n", "import { useEffect, useLayoutEffect } from \"react\"\n\n/**\n * Sort an array of DOM nodes according to the HTML tree order\n * @see http://www.w3.org/TR/html5/infrastructure.html#tree-order\n */\nexport function sortNodes(nodes: Node[]) {\n  return nodes.sort((a, b) => {\n    const compare = a.compareDocumentPosition(b)\n\n    if (\n      compare & Node.DOCUMENT_POSITION_FOLLOWING ||\n      compare & Node.DOCUMENT_POSITION_CONTAINED_BY\n    ) {\n      // a < b\n      return -1\n    }\n\n    if (\n      compare & Node.DOCUMENT_POSITION_PRECEDING ||\n      compare & Node.DOCUMENT_POSITION_CONTAINS\n    ) {\n      // a > b\n      return 1\n    }\n\n    if (\n      compare & Node.DOCUMENT_POSITION_DISCONNECTED ||\n      compare & Node.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC\n    ) {\n      throw Error(\"Cannot sort the given nodes.\")\n    } else {\n      return 0\n    }\n  })\n}\n\nexport const isElement = (el: any): el is HTMLElement =>\n  typeof el == \"object\" && \"nodeType\" in el && el.nodeType === Node.ELEMENT_NODE\n\nexport function getNextIndex(current: number, max: number, loop: boolean) {\n  let next = current + 1\n  if (loop && next >= max) next = 0\n  return next\n}\n\nexport function getPrevIndex(current: number, max: number, loop: boolean) {\n  let next = current - 1\n  if (loop && next < 0) next = max\n  return next\n}\n\nexport const useSafeLayoutEffect =\n  typeof window !== \"undefined\" ? useLayoutEffect : useEffect\n\nexport const cast = <T>(value: any) => value as T\n", "import { sortNodes, isElement, getNextIndex, getPrevIndex } from \"./utils\"\n\nexport type DescendantOptions<T = {}> = T & {\n  /**\n   * If `true`, the item will be registered in all nodes map\n   * but omitted from enabled nodes map\n   */\n  disabled?: boolean\n  /**\n   * The id of the item\n   */\n  id?: string\n}\n\nexport type Descendant<T, K> = DescendantOptions<K> & {\n  /**\n   * DOM element of the item\n   */\n  node: T\n  /**\n   * index of item in all nodes map and enabled nodes map\n   */\n  index: number\n}\n\n/**\n * @internal\n *\n * Class to manage descendants and their relative indices in the DOM.\n * It uses `node.compareDocumentPosition(...)` under the hood\n */\nexport class DescendantsManager<\n  T extends HTMLElement,\n  K extends Record<string, any> = {},\n> {\n  private descendants = new Map<T, Descendant<T, K>>()\n\n  register = (nodeOrOptions: T | null | DescendantOptions<K>) => {\n    if (nodeOrOptions == null) return\n\n    if (isElement(nodeOrOptions)) {\n      return this.registerNode(nodeOrOptions)\n    }\n\n    return (node: T | null) => {\n      this.registerNode(node, nodeOrOptions)\n    }\n  }\n\n  unregister = (node: T) => {\n    this.descendants.delete(node)\n    const sorted = sortNodes(Array.from(this.descendants.keys()))\n    this.assignIndex(sorted)\n  }\n\n  destroy = () => {\n    this.descendants.clear()\n  }\n\n  private assignIndex = (descendants: Node[]) => {\n    this.descendants.forEach((descendant) => {\n      const index = descendants.indexOf(descendant.node)\n      descendant.index = index\n      descendant.node.dataset[\"index\"] = descendant.index.toString()\n    })\n  }\n\n  count = () => this.descendants.size\n\n  enabledCount = () => this.enabledValues().length\n\n  values = () => {\n    const values = Array.from(this.descendants.values())\n    return values.sort((a, b) => a.index - b.index)\n  }\n\n  enabledValues = () => {\n    return this.values().filter((descendant) => !descendant.disabled)\n  }\n\n  item = (index: number) => {\n    if (this.count() === 0) return undefined\n    return this.values()[index]\n  }\n\n  enabledItem = (index: number) => {\n    if (this.enabledCount() === 0) return undefined\n    return this.enabledValues()[index]\n  }\n\n  first = () => this.item(0)\n\n  firstEnabled = () => this.enabledItem(0)\n\n  last = () => this.item(this.descendants.size - 1)\n\n  lastEnabled = () => {\n    const lastIndex = this.enabledValues().length - 1\n    return this.enabledItem(lastIndex)\n  }\n\n  indexOf = (node: T | null) => {\n    if (!node) return -1\n    return this.descendants.get(node)?.index ?? -1\n  }\n\n  enabledIndexOf = (node: T | null) => {\n    if (node == null) return -1\n    return this.enabledValues().findIndex((i) => i.node.isSameNode(node))\n  }\n\n  next = (index: number, loop = true) => {\n    const next = getNextIndex(index, this.count(), loop)\n    return this.item(next)\n  }\n\n  nextEnabled = (index: number, loop = true) => {\n    const item = this.item(index)\n    if (!item) return\n    const enabledIndex = this.enabledIndexOf(item.node)\n    const nextEnabledIndex = getNextIndex(\n      enabledIndex,\n      this.enabledCount(),\n      loop,\n    )\n    return this.enabledItem(nextEnabledIndex)\n  }\n\n  prev = (index: number, loop = true) => {\n    const prev = getPrevIndex(index, this.count() - 1, loop)\n    return this.item(prev)\n  }\n\n  prevEnabled = (index: number, loop = true) => {\n    const item = this.item(index)\n    if (!item) return\n    const enabledIndex = this.enabledIndexOf(item.node)\n    const prevEnabledIndex = getPrevIndex(\n      enabledIndex,\n      this.enabledCount() - 1,\n      loop,\n    )\n    return this.enabledItem(prevEnabledIndex)\n  }\n\n  private registerNode = (node: T | null, options?: DescendantOptions<K>) => {\n    if (!node || this.descendants.has(node)) return\n\n    const keys = Array.from(this.descendants.keys()).concat(node)\n    const sorted = sortNodes(keys)\n\n    if (options?.disabled) {\n      options.disabled = !!options.disabled\n    }\n\n    const descendant = { node, index: -1, ...options }\n\n    this.descendants.set(node, descendant as Descendant<T, K>)\n\n    this.assignIndex(sorted)\n  }\n}\n", "import { createContext } from \"@chakra-ui/react-context\"\nimport { mergeRefs } from \"@chakra-ui/react-use-merge-refs\"\nimport { useRef, useState } from \"react\"\nimport { DescendantsManager, DescendantOptions } from \"./descendant\"\nimport { useSafeLayoutEffect, cast } from \"./utils\"\n\n/**\n * @internal\n * React hook that initializes the DescendantsManager\n */\nfunction useDescendants<\n  T extends HTMLElement = HTMLElement,\n  K extends Record<string, any> = {},\n>() {\n  const descendants = useRef(new DescendantsManager<T, K>())\n  useSafeLayoutEffect(() => {\n    return () => descendants.current.destroy()\n  })\n  return descendants.current\n}\n\nexport interface UseDescendantsReturn\n  extends ReturnType<typeof useDescendants> {}\n\n/* -------------------------------------------------------------------------------------------------\n * Descendants context to be used in component-land.\n  - Mount the `DescendantsContextProvider` at the root of the component\n  - Call `useDescendantsContext` anywhere you need access to the descendants information\n\n  NB:  I recommend using `createDescendantContext` below\n * -----------------------------------------------------------------------------------------------*/\n\nconst [DescendantsContextProvider, useDescendantsContext] =\n  createContext<UseDescendantsReturn>({\n    name: \"DescendantsProvider\",\n    errorMessage:\n      \"useDescendantsContext must be used within DescendantsProvider\",\n  })\n\n/**\n * @internal\n * This hook provides information a descendant such as:\n * - Its index compared to other descendants\n * - ref callback to register the descendant\n * - Its enabled index compared to other enabled descendants\n */\nfunction useDescendant<\n  T extends HTMLElement = HTMLElement,\n  K extends Record<string, any> = {},\n>(options?: DescendantOptions<K>) {\n  const descendants = useDescendantsContext()\n  const [index, setIndex] = useState(-1)\n  const ref = useRef<T>(null)\n\n  useSafeLayoutEffect(() => {\n    return () => {\n      if (!ref.current) return\n      descendants.unregister(ref.current)\n    }\n  }, [])\n\n  useSafeLayoutEffect(() => {\n    if (!ref.current) return\n    const dataIndex = Number(ref.current.dataset[\"index\"])\n    if (index != dataIndex && !Number.isNaN(dataIndex)) {\n      setIndex(dataIndex)\n    }\n  })\n\n  const refCallback = options\n    ? cast<React.RefCallback<T>>(descendants.register(options))\n    : cast<React.RefCallback<T>>(descendants.register)\n\n  return {\n    descendants,\n    index,\n    enabledIndex: descendants.enabledIndexOf(ref.current),\n    register: mergeRefs(refCallback, ref),\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Function that provides strongly typed versions of the context provider and hooks above.\n   To be used in component-land\n * -----------------------------------------------------------------------------------------------*/\n\nexport function createDescendantContext<\n  T extends HTMLElement = HTMLElement,\n  K extends Record<string, any> = {},\n>() {\n  type ContextProviderType = React.Provider<DescendantsManager<T, K>>\n  const ContextProvider = cast<ContextProviderType>(DescendantsContextProvider)\n\n  const _useDescendantsContext = () =>\n    cast<DescendantsManager<T, K>>(useDescendantsContext())\n\n  const _useDescendant = (options?: DescendantOptions<K>) =>\n    useDescendant<T, K>(options)\n\n  const _useDescendants = () => useDescendants<T, K>()\n\n  return [\n    // context provider\n    ContextProvider,\n    // call this when you need to read from context\n    _useDescendantsContext,\n    // descendants state information, to be called and passed to `ContextProvider`\n    _useDescendants,\n    // descendant index information\n    _useDescendant,\n  ] as const\n}\n", "// src/dom.ts\nfunction isElement(el) {\n  return el != null && typeof el == \"object\" && \"nodeType\" in el && el.nodeType === Node.ELEMENT_NODE;\n}\nfunction isHTMLElement(el) {\n  var _a;\n  if (!isElement(el))\n    return false;\n  const win = (_a = el.ownerDocument.defaultView) != null ? _a : window;\n  return el instanceof win.HTMLElement;\n}\nfunction getOwnerWindow(node) {\n  var _a, _b;\n  return (_b = (_a = getOwnerDocument(node)) == null ? void 0 : _a.defaultView) != null ? _b : window;\n}\nfunction getOwnerDocument(node) {\n  return isElement(node) ? node.ownerDocument : document;\n}\nfunction getEventWindow(event) {\n  var _a;\n  return (_a = event.view) != null ? _a : window;\n}\nfunction isBrowser() {\n  return Boolean(globalThis == null ? void 0 : globalThis.document);\n}\nfunction getActiveElement(node) {\n  return getOwnerDocument(node).activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent)\n    return false;\n  return parent === child || parent.contains(child);\n}\n\nexport {\n  isElement,\n  isHTMLElement,\n  getOwnerWindow,\n  getOwnerDocument,\n  getEventWindow,\n  isBrowser,\n  getActiveElement,\n  contains\n};\n", "import {\n  getOwnerDocument,\n  isHTMLElement\n} from \"./chunk-3XANSPY5.mjs\";\n\n// src/tabbable.ts\nvar hasDisplayNone = (element) => window.getComputedStyle(element).display === \"none\";\nvar hasTabIndex = (element) => element.hasAttribute(\"tabindex\");\nvar hasNegativeTabIndex = (element) => hasTabIndex(element) && element.tabIndex === -1;\nfunction isDisabled(element) {\n  return Boolean(element.getAttribute(\"disabled\")) === true || Boolean(element.getAttribute(\"aria-disabled\")) === true;\n}\nfunction isInputElement(element) {\n  return isHTMLElement(element) && element.localName === \"input\" && \"select\" in element;\n}\nfunction isActiveElement(element) {\n  const doc = isHTMLElement(element) ? getOwnerDocument(element) : document;\n  return doc.activeElement === element;\n}\nfunction hasFocusWithin(element) {\n  if (!document.activeElement)\n    return false;\n  return element.contains(document.activeElement);\n}\nfunction isHidden(element) {\n  if (element.parentElement && isHidden(element.parentElement))\n    return true;\n  return element.hidden;\n}\nfunction isContentEditable(element) {\n  const value = element.getAttribute(\"contenteditable\");\n  return value !== \"false\" && value != null;\n}\nfunction isFocusable(element) {\n  if (!isHTMLElement(element) || isHidden(element) || isDisabled(element)) {\n    return false;\n  }\n  const { localName } = element;\n  const focusableTags = [\"input\", \"select\", \"textarea\", \"button\"];\n  if (focusableTags.indexOf(localName) >= 0)\n    return true;\n  const others = {\n    a: () => element.hasAttribute(\"href\"),\n    audio: () => element.hasAttribute(\"controls\"),\n    video: () => element.hasAttribute(\"controls\")\n  };\n  if (localName in others) {\n    return others[localName]();\n  }\n  if (isContentEditable(element))\n    return true;\n  return hasTabIndex(element);\n}\nfunction isTabbable(element) {\n  if (!element)\n    return false;\n  return isHTMLElement(element) && isFocusable(element) && !hasNegativeTabIndex(element);\n}\n\nexport {\n  hasDisplayNone,\n  hasTabIndex,\n  hasNegativeTabIndex,\n  isDisabled,\n  isInputElement,\n  isActiveElement,\n  hasFocusWithin,\n  isHidden,\n  isContentEditable,\n  isFocusable,\n  isTabbable\n};\n", "import {\n  getScrollParent\n} from \"./chunk-4WEUWBTD.mjs\";\nimport {\n  hasDisplayNone,\n  hasFocusWithin,\n  hasNegativeTabIndex,\n  hasTabIndex,\n  isActiveElement,\n  isContentEditable,\n  isDisabled,\n  isFocusable,\n  isHidden,\n  isInputElement,\n  isTabbable\n} from \"./chunk-ROURZMX4.mjs\";\nimport {\n  contains,\n  getActiveElement,\n  getEventWindow,\n  getOwnerDocument,\n  getOwnerWindow,\n  isBrowser,\n  isElement,\n  isHTMLElement\n} from \"./chunk-3XANSPY5.mjs\";\n\n// src/index.ts\nvar focusableElList = [\n  \"input:not(:disabled):not([disabled])\",\n  \"select:not(:disabled):not([disabled])\",\n  \"textarea:not(:disabled):not([disabled])\",\n  \"embed\",\n  \"iframe\",\n  \"object\",\n  \"a[href]\",\n  \"area[href]\",\n  \"button:not(:disabled):not([disabled])\",\n  \"[tabindex]\",\n  \"audio[controls]\",\n  \"video[controls]\",\n  \"*[tabindex]:not([aria-disabled])\",\n  \"*[contenteditable]\"\n];\nvar focusableElSelector = focusableElList.join();\nvar isVisible = (el) => el.offsetWidth > 0 && el.offsetHeight > 0;\nfunction getAllFocusable(container) {\n  const focusableEls = Array.from(\n    container.querySelectorAll(focusableElSelector)\n  );\n  focusableEls.unshift(container);\n  return focusableEls.filter((el) => isFocusable(el) && isVisible(el));\n}\nfunction getFirstFocusable(container) {\n  const allFocusable = getAllFocusable(container);\n  return allFocusable.length ? allFocusable[0] : null;\n}\nfunction getAllTabbable(container, fallbackToFocusable) {\n  const allFocusable = Array.from(\n    container.querySelectorAll(focusableElSelector)\n  );\n  const allTabbable = allFocusable.filter(isTabbable);\n  if (isTabbable(container)) {\n    allTabbable.unshift(container);\n  }\n  if (!allTabbable.length && fallbackToFocusable) {\n    return allFocusable;\n  }\n  return allTabbable;\n}\nfunction getFirstTabbableIn(container, fallbackToFocusable) {\n  const [first] = getAllTabbable(container, fallbackToFocusable);\n  return first || null;\n}\nfunction getLastTabbableIn(container, fallbackToFocusable) {\n  const allTabbable = getAllTabbable(container, fallbackToFocusable);\n  return allTabbable[allTabbable.length - 1] || null;\n}\nfunction getNextTabbable(container, fallbackToFocusable) {\n  const allFocusable = getAllFocusable(container);\n  const index = allFocusable.indexOf(document.activeElement);\n  const slice = allFocusable.slice(index + 1);\n  return slice.find(isTabbable) || allFocusable.find(isTabbable) || (fallbackToFocusable ? slice[0] : null);\n}\nfunction getPreviousTabbable(container, fallbackToFocusable) {\n  const allFocusable = getAllFocusable(container).reverse();\n  const index = allFocusable.indexOf(document.activeElement);\n  const slice = allFocusable.slice(index + 1);\n  return slice.find(isTabbable) || allFocusable.find(isTabbable) || (fallbackToFocusable ? slice[0] : null);\n}\nexport {\n  contains,\n  getActiveElement,\n  getAllFocusable,\n  getAllTabbable,\n  getEventWindow,\n  getFirstFocusable,\n  getFirstTabbableIn,\n  getLastTabbableIn,\n  getNextTabbable,\n  getOwnerDocument,\n  getOwnerWindow,\n  getPreviousTabbable,\n  getScrollParent,\n  hasDisplayNone,\n  hasFocusWithin,\n  hasNegativeTabIndex,\n  hasTabIndex,\n  isActiveElement,\n  isBrowser,\n  isContentEditable,\n  isDisabled,\n  isElement,\n  isFocusable,\n  isHTMLElement,\n  isHidden,\n  isInputElement,\n  isTabbable\n};\n", "import { useEffect } from \"react\"\nimport { useCallbackRef } from \"@chakra-ui/react-use-callback-ref\"\n\ntype Target = EventTarget | null | (() => EventTarget | null)\ntype Options = boolean | AddEventListenerOptions\n\nexport function useEventListener<K extends keyof DocumentEventMap>(\n  target: Target,\n  event: K,\n  handler?: (event: DocumentEventMap[K]) => void,\n  options?: Options,\n): VoidFunction\nexport function useEventListener<K extends keyof WindowEventMap>(\n  target: Target,\n  event: K,\n  handler?: (event: WindowEventMap[K]) => void,\n  options?: Options,\n): VoidFunction\nexport function useEventListener<K extends keyof GlobalEventHandlersEventMap>(\n  target: Target,\n  event: K,\n  handler?: (event: GlobalEventHandlersEventMap[K]) => void,\n  options?: Options,\n): VoidFunction\nexport function useEventListener(\n  target: Target,\n  event: string,\n  handler: ((event: Event) => void) | undefined,\n  options?: Options,\n) {\n  const listener = useCallbackRef(handler)\n\n  useEffect(() => {\n    const node = typeof target === \"function\" ? target() : target ?? document\n\n    if (!handler || !node) return\n\n    node.addEventListener(event, listener, options)\n    return () => {\n      node.removeEventListener(event, listener, options)\n    }\n  }, [event, target, options, listener, handler])\n\n  return () => {\n    const node = typeof target === \"function\" ? target() : target ?? document\n    node?.removeEventListener(event, listener, options)\n  }\n}\n", "import {\n  FocusableElement,\n  getActiveElement,\n  getAllFocusable,\n  isTabbable,\n} from \"@chakra-ui/dom-utils\"\nimport { useEventListener } from \"@chakra-ui/react-use-event-listener\"\nimport { useSafeLayoutEffect } from \"@chakra-ui/react-use-safe-layout-effect\"\nimport { useUpdateEffect } from \"@chakra-ui/react-use-update-effect\"\nimport type { RefObject } from \"react\"\nimport { useCallback, useRef } from \"react\"\n\nexport interface UseFocusOnHideOptions {\n  focusRef: RefObject<FocusableElement>\n  shouldFocus?: boolean\n  visible?: boolean\n}\n\nfunction preventReturnFocus(containerRef: React.RefObject<HTMLElement>) {\n  const el = containerRef.current\n  if (!el) return false\n\n  const activeElement = getActiveElement(el)\n\n  if (!activeElement) return false\n  if (el.contains(activeElement)) return false\n  if (isTabbable(activeElement)) return true\n\n  return false\n}\n\n/**\n * Popover hook to manage the focus when the popover closes or hides.\n *\n * We either want to return focus back to the popover trigger or\n * let focus proceed normally if user moved to another interactive\n * element in the viewport.\n */\nexport function useFocusOnHide(\n  containerRef: RefObject<HTMLElement>,\n  options: UseFocusOnHideOptions,\n) {\n  const { shouldFocus: shouldFocusProp, visible, focusRef } = options\n\n  const shouldFocus = shouldFocusProp && !visible\n\n  useUpdateEffect(() => {\n    if (!shouldFocus) return\n\n    if (preventReturnFocus(containerRef)) {\n      return\n    }\n\n    const el = focusRef?.current || containerRef.current\n\n    let rafId: number\n\n    if (el) {\n      rafId = requestAnimationFrame(() => {\n        el.focus({ preventScroll: true })\n      })\n      return () => {\n        cancelAnimationFrame(rafId)\n      }\n    }\n  }, [shouldFocus, containerRef, focusRef])\n}\n\nexport interface UseFocusOnShowOptions {\n  visible?: boolean\n  shouldFocus?: boolean\n  preventScroll?: boolean\n  focusRef?: React.RefObject<FocusableElement>\n}\n\nconst defaultOptions: UseFocusOnShowOptions = {\n  preventScroll: true,\n  shouldFocus: false,\n}\n\nexport function useFocusOnShow<T extends HTMLElement>(\n  target: React.RefObject<T> | T,\n  options = defaultOptions,\n) {\n  const { focusRef, preventScroll, shouldFocus, visible } = options\n  const element = isRefObject(target) ? target.current : target\n\n  const autoFocusValue = shouldFocus && visible\n  const autoFocusRef = useRef(autoFocusValue)\n  const lastVisibleRef = useRef(visible)\n\n  useSafeLayoutEffect(() => {\n    if (!lastVisibleRef.current && visible) {\n      autoFocusRef.current = autoFocusValue\n    }\n    lastVisibleRef.current = visible\n  }, [visible, autoFocusValue])\n\n  const onFocus = useCallback(() => {\n    if (!visible || !element || !autoFocusRef.current) return\n    autoFocusRef.current = false\n\n    if (element.contains(document.activeElement as HTMLElement)) return\n\n    if (focusRef?.current) {\n      requestAnimationFrame(() => {\n        focusRef.current?.focus({ preventScroll })\n      })\n    } else {\n      const tabbableEls = getAllFocusable(element)\n      if (tabbableEls.length > 0) {\n        requestAnimationFrame(() => {\n          tabbableEls[0].focus({ preventScroll })\n        })\n      }\n    }\n  }, [visible, preventScroll, element, focusRef])\n\n  useUpdateEffect(() => {\n    onFocus()\n  }, [onFocus])\n\n  useEventListener(element, \"transitionend\", onFocus)\n}\n\nfunction isRefObject(val: any): val is { current: any } {\n  return \"current\" in val\n}\n", "import { Placement } from \"@popperjs/core\"\n\nconst toVar = (value: string, fallback?: string) => ({\n  var: value,\n  varRef: fallback ? `var(${value}, ${fallback})` : `var(${value})`,\n})\n\nexport const cssVars = {\n  arrowShadowColor: toVar(\"--popper-arrow-shadow-color\"),\n  arrowSize: toVar(\"--popper-arrow-size\", \"8px\"),\n  arrowSizeHalf: toVar(\"--popper-arrow-size-half\"),\n  arrowBg: toVar(\"--popper-arrow-bg\"),\n  transformOrigin: toVar(\"--popper-transform-origin\"),\n  arrowOffset: toVar(\"--popper-arrow-offset\"),\n} as const\n\nexport function getBoxShadow(placement: Placement) {\n  if (placement.includes(\"top\"))\n    return `1px 1px 0px 0 var(--popper-arrow-shadow-color)`\n  if (placement.includes(\"bottom\"))\n    return `-1px -1px 0px 0 var(--popper-arrow-shadow-color)`\n  if (placement.includes(\"right\"))\n    return `-1px 1px 0px 0 var(--popper-arrow-shadow-color)`\n  if (placement.includes(\"left\"))\n    return `1px -1px 0px 0 var(--popper-arrow-shadow-color)`\n}\n\nconst transforms: Record<string, string> = {\n  top: \"bottom center\",\n  \"top-start\": \"bottom left\",\n  \"top-end\": \"bottom right\",\n\n  bottom: \"top center\",\n  \"bottom-start\": \"top left\",\n  \"bottom-end\": \"top right\",\n\n  left: \"right center\",\n  \"left-start\": \"right top\",\n  \"left-end\": \"right bottom\",\n\n  right: \"left center\",\n  \"right-start\": \"left top\",\n  \"right-end\": \"left bottom\",\n}\n\nexport const toTransformOrigin = (placement: Placement) => transforms[placement]\n\nconst defaultEventListeners = {\n  scroll: true,\n  resize: true,\n}\n\nexport function getEventListenerOptions(\n  value?: boolean | Partial<typeof defaultEventListeners>,\n) {\n  let eventListeners: {\n    enabled?: boolean\n    options?: typeof defaultEventListeners\n  }\n  if (typeof value === \"object\") {\n    eventListeners = {\n      enabled: true,\n      options: { ...defaultEventListeners, ...value },\n    }\n  } else {\n    eventListeners = {\n      enabled: value,\n      options: defaultEventListeners,\n    }\n  }\n  return eventListeners\n}\n", "import { Placement, Modifier, State } from \"@popperjs/core\"\nimport { getBoxShadow, toTransformOrigin, cssVars } from \"./utils\"\n\n/* -------------------------------------------------------------------------------------------------\n The match width modifier sets the popper width to match the reference.\n It is useful for custom selects, autocomplete, etc.\n* -----------------------------------------------------------------------------------------------*/\n\nexport const matchWidth: Modifier<\"matchWidth\", any> = {\n  name: \"matchWidth\",\n  enabled: true,\n  phase: \"beforeWrite\",\n  requires: [\"computeStyles\"],\n  fn: ({ state }) => {\n    state.styles.popper.width = `${state.rects.reference.width}px`\n  },\n  effect:\n    ({ state }) =>\n    () => {\n      const reference = state.elements.reference as HTMLElement\n      state.elements.popper.style.width = `${reference.offsetWidth}px`\n    },\n}\n\n/* -------------------------------------------------------------------------------------------------\n  The transform origin modifier sets the css `transformOrigin` value of the popper\n  based on the dynamic placement state of the popper.\n  \n  Useful when we need to animate/transition the popper.\n* -----------------------------------------------------------------------------------------------*/\n\nexport const transformOrigin: Modifier<\"transformOrigin\", any> = {\n  name: \"transformOrigin\",\n  enabled: true,\n  phase: \"write\",\n  fn: ({ state }) => {\n    setTransformOrigin(state)\n  },\n  effect:\n    ({ state }) =>\n    () => {\n      setTransformOrigin(state)\n    },\n}\n\nconst setTransformOrigin = (state: State) => {\n  state.elements.popper.style.setProperty(\n    cssVars.transformOrigin.var,\n    toTransformOrigin(state.placement),\n  )\n}\n\n/* -------------------------------------------------------------------------------------------------\n  The position arrow modifier adds width, height and overrides the `top/left/right/bottom`\n  styles generated by popper.js to properly position the arrow\n* -----------------------------------------------------------------------------------------------*/\n\nexport const positionArrow: Modifier<\"positionArrow\", any> = {\n  name: \"positionArrow\",\n  enabled: true,\n  phase: \"afterWrite\",\n  fn: ({ state }) => {\n    setArrowStyles(state)\n  },\n}\n\nconst setArrowStyles = (state: Partial<State>) => {\n  if (!state.placement) return\n  const overrides = getArrowStyle(state.placement)\n\n  if (state.elements?.arrow && overrides) {\n    Object.assign(state.elements.arrow.style, {\n      [overrides.property]: overrides.value,\n      width: cssVars.arrowSize.varRef,\n      height: cssVars.arrowSize.varRef,\n      zIndex: -1,\n    })\n\n    const vars = {\n      [cssVars.arrowSizeHalf\n        .var]: `calc(${cssVars.arrowSize.varRef} / 2 - 1px)`,\n      [cssVars.arrowOffset.var]: `calc(${cssVars.arrowSizeHalf.varRef} * -1)`,\n    }\n\n    for (const property in vars) {\n      state.elements.arrow.style.setProperty(property, vars[property])\n    }\n  }\n}\n\nconst getArrowStyle = (placement: Placement) => {\n  if (placement.startsWith(\"top\")) {\n    return { property: \"bottom\", value: cssVars.arrowOffset.varRef }\n  }\n  if (placement.startsWith(\"bottom\")) {\n    return { property: \"top\", value: cssVars.arrowOffset.varRef }\n  }\n  if (placement.startsWith(\"left\")) {\n    return { property: \"right\", value: cssVars.arrowOffset.varRef }\n  }\n  if (placement.startsWith(\"right\")) {\n    return { property: \"left\", value: cssVars.arrowOffset.varRef }\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n  The inner arrow modifier, sets the placement styles for the inner arrow that forms\n  the popper arrow tip.\n* -----------------------------------------------------------------------------------------------*/\n\nexport const innerArrow: Modifier<\"innerArrow\", any> = {\n  name: \"innerArrow\",\n  enabled: true,\n  phase: \"main\",\n  requires: [\"arrow\"],\n  fn: ({ state }) => {\n    setInnerArrowStyles(state)\n  },\n  effect:\n    ({ state }) =>\n    () => {\n      setInnerArrowStyles(state)\n    },\n}\n\nconst setInnerArrowStyles = (state: State) => {\n  if (!state.elements.arrow) return\n\n  const inner = state.elements.arrow.querySelector(\n    \"[data-popper-arrow-inner]\",\n  ) as HTMLElement | null\n\n  if (!inner) return\n  const boxShadow = getBoxShadow(state.placement)\n  if (boxShadow) {\n    inner.style.setProperty(\"--popper-arrow-default-shadow\", boxShadow)\n  }\n\n  Object.assign(inner.style, {\n    transform: \"rotate(45deg)\",\n    background: cssVars.arrowBg.varRef,\n    top: 0,\n    left: 0,\n    width: \"100%\",\n    height: \"100%\",\n    position: \"absolute\",\n    zIndex: \"inherit\",\n    boxShadow: `var(--popper-arrow-shadow, var(--popper-arrow-default-shadow))`,\n  })\n}\n", "import type { Placement } from \"@popperjs/core\"\n\ntype Logical =\n  | \"start-start\"\n  | \"start-end\"\n  | \"end-start\"\n  | \"end-end\"\n  | \"start\"\n  | \"end\"\n\ntype PlacementWithLogical = Placement | Logical\n\nexport type { Placement, PlacementWithLogical }\n\nconst logicals: Record<Logical, { ltr: Placement; rtl: Placement }> = {\n  \"start-start\": { ltr: \"left-start\", rtl: \"right-start\" },\n  \"start-end\": { ltr: \"left-end\", rtl: \"right-end\" },\n  \"end-start\": { ltr: \"right-start\", rtl: \"left-start\" },\n  \"end-end\": { ltr: \"right-end\", rtl: \"left-end\" },\n  start: { ltr: \"left\", rtl: \"right\" },\n  end: { ltr: \"right\", rtl: \"left\" },\n}\n\nconst opposites: Partial<Record<Placement, Placement>> = {\n  \"auto-start\": \"auto-end\",\n  \"auto-end\": \"auto-start\",\n  \"top-start\": \"top-end\",\n  \"top-end\": \"top-start\",\n  \"bottom-start\": \"bottom-end\",\n  \"bottom-end\": \"bottom-start\",\n}\n\nexport function getPopperPlacement(\n  placement: PlacementWithLogical,\n  dir: \"ltr\" | \"rtl\" = \"ltr\",\n): Placement {\n  const value = ((logicals as any)[placement]?.[dir] || placement) as Placement\n  if (dir === \"ltr\") return value\n  return (opposites as any)[placement] ?? value\n}\n", "import { mergeRefs } from \"@chakra-ui/react-use-merge-refs\"\nimport { PropGetter } from \"@chakra-ui/react-types\"\nimport {\n  createPopper,\n  Instance,\n  Modifier,\n  VirtualElement,\n} from \"@popperjs/core\"\nimport { useCallback, useEffect, useRef } from \"react\"\nimport * as customModifiers from \"./modifiers\"\nimport { getPopperPlacement, PlacementWithLogical } from \"./popper.placement\"\nimport { cssVars, getEventListenerOptions } from \"./utils\"\n\nexport interface UsePopperProps {\n  /**\n   * Whether the popper.js should be enabled\n   */\n  enabled?: boolean\n  /**\n   * The main and cross-axis offset to displace popper element\n   * from its reference element.\n   */\n  offset?: [number, number]\n  /**\n   * The distance or margin between the reference and popper.\n   * It is used internally to create an `offset` modifier.\n   *\n   * NB: If you define `offset` prop, it'll override the gutter.\n   * @default 8\n   */\n  gutter?: number\n  /**\n   * If `true`, will prevent the popper from being cut off and ensure\n   * it's visible within the boundary area.\n   * @default true\n   */\n  preventOverflow?: boolean\n  /**\n   * If `true`, the popper will change its placement and flip when it's\n   * about to overflow its boundary area.\n   * @default true\n   */\n  flip?: boolean\n  /**\n   * If `true`, the popper will match the width of the reference at all times.\n   * It's useful for `autocomplete`, `date-picker` and `select` patterns.\n   * @default false\n   */\n  matchWidth?: boolean\n  /**\n   * The boundary area for the popper. Used within the `preventOverflow` modifier\n   * @default \"clippingParents\"\n   */\n  boundary?: \"clippingParents\" | \"scrollParent\" | HTMLElement\n  /**\n   * If provided, determines whether the popper will reposition itself on `scroll`\n   * and `resize` of the window.\n   * @default true\n   */\n  eventListeners?: boolean | { scroll?: boolean; resize?: boolean }\n  /**\n   * The padding required to prevent the arrow from\n   * reaching the very edge of the popper.\n   * @default 8\n   */\n  arrowPadding?: number\n  /**\n   * The CSS positioning strategy to use.\n   * @default \"absolute\"\n   */\n  strategy?: \"absolute\" | \"fixed\"\n  /**\n   * The placement of the popper relative to its reference.\n   * @default \"bottom\"\n   */\n  placement?: PlacementWithLogical\n  /**\n   * Array of popper.js modifiers. Check the docs to see\n   * the list of possible modifiers you can pass.\n   *\n   * @see Docs https://popper.js.org/docs/v2/modifiers/\n   */\n  modifiers?: Array<Partial<Modifier<string, any>>>\n  /**\n   * Theme direction `ltr` or `rtl`. Popper's placement will\n   * be set accordingly\n   * @default \"ltr\"\n   */\n  direction?: \"ltr\" | \"rtl\"\n}\n\nexport type ArrowCSSVarProps = {\n  /**\n   * The size of the popover arrow.\n   * This sets the `--popper-arrow-size` css property\n   */\n  size?: string | number\n  /**\n   * The box-shadow color of the popover arrow.\n   * This sets the `--popper-arrow-shadow-color` css property\n   */\n  shadowColor?: string\n  /**\n   * The background color of the popper arrow.\n   * This sets the `--popper-arrow-bg` css property.\n   */\n  bg?: string\n}\n\nexport function usePopper(props: UsePopperProps = {}) {\n  const {\n    enabled = true,\n    modifiers,\n    placement: placementProp = \"bottom\",\n    strategy = \"absolute\",\n    arrowPadding = 8,\n    eventListeners = true,\n    offset,\n    gutter = 8,\n    flip = true,\n    boundary = \"clippingParents\",\n    preventOverflow = true,\n    matchWidth,\n    direction = \"ltr\",\n  } = props\n\n  const reference = useRef<Element | VirtualElement | null>(null)\n  const popper = useRef<HTMLElement | null>(null)\n  const instance = useRef<Instance | null>(null)\n  const placement = getPopperPlacement(placementProp, direction)\n\n  const cleanup = useRef(() => {})\n\n  const setupPopper = useCallback(() => {\n    if (!enabled || !reference.current || !popper.current) return\n\n    // If popper instance exists, destroy it, so we can create a new one\n    cleanup.current?.()\n\n    instance.current = createPopper(reference.current, popper.current, {\n      placement,\n      modifiers: [\n        customModifiers.innerArrow,\n        customModifiers.positionArrow,\n        customModifiers.transformOrigin,\n        {\n          ...customModifiers.matchWidth,\n          enabled: !!matchWidth,\n        },\n        {\n          name: \"eventListeners\",\n          ...getEventListenerOptions(eventListeners),\n        },\n        {\n          name: \"arrow\",\n          options: { padding: arrowPadding },\n        },\n        {\n          name: \"offset\",\n          options: {\n            offset: offset ?? [0, gutter],\n          },\n        },\n        {\n          name: \"flip\",\n          enabled: !!flip,\n          options: { padding: 8 },\n        },\n        {\n          name: \"preventOverflow\",\n          enabled: !!preventOverflow,\n          options: { boundary },\n        },\n        // allow users override internal modifiers\n        ...(modifiers ?? []),\n      ],\n      strategy,\n    })\n\n    // force update one-time to fix any positioning issues\n    instance.current.forceUpdate()\n\n    cleanup.current = instance.current.destroy\n  }, [\n    placement,\n    enabled,\n    modifiers,\n    matchWidth,\n    eventListeners,\n    arrowPadding,\n    offset,\n    gutter,\n    flip,\n    preventOverflow,\n    boundary,\n    strategy,\n  ])\n\n  useEffect(() => {\n    return () => {\n      /**\n       * Fast refresh might call this function and tear down the popper\n       * even if the reference still exists. This checks against that\n       */\n      if (!reference.current && !popper.current) {\n        instance.current?.destroy()\n        instance.current = null\n      }\n    }\n  }, [])\n\n  const referenceRef = useCallback(\n    <T extends Element | VirtualElement>(node: T | null) => {\n      reference.current = node\n      setupPopper()\n    },\n    [setupPopper],\n  )\n\n  const getReferenceProps: PropGetter = useCallback(\n    (props = {}, ref = null) => ({\n      ...props,\n      ref: mergeRefs(referenceRef, ref),\n    }),\n    [referenceRef],\n  )\n\n  const popperRef = useCallback(\n    <T extends HTMLElement>(node: T | null) => {\n      popper.current = node\n      setupPopper()\n    },\n    [setupPopper],\n  )\n\n  const getPopperProps: PropGetter = useCallback(\n    (props = {}, ref = null) => ({\n      ...props,\n      ref: mergeRefs(popperRef, ref),\n      style: {\n        ...props.style,\n        position: strategy,\n        minWidth: matchWidth ? undefined : \"max-content\",\n        inset: \"0 auto auto 0\",\n      },\n    }),\n    [strategy, popperRef, matchWidth],\n  )\n\n  const getArrowProps: PropGetter = useCallback((props = {}, ref = null) => {\n    const { size, shadowColor, bg, style, ...rest } = props\n    return {\n      ...rest,\n      ref,\n      \"data-popper-arrow\": \"\",\n      style: getArrowStyle(props),\n    }\n  }, [])\n\n  const getArrowInnerProps: PropGetter = useCallback(\n    (props = {}, ref = null) => ({\n      ...props,\n      ref,\n      \"data-popper-arrow-inner\": \"\",\n    }),\n    [],\n  )\n\n  return {\n    update() {\n      instance.current?.update()\n    },\n    forceUpdate() {\n      instance.current?.forceUpdate()\n    },\n    transformOrigin: cssVars.transformOrigin.varRef,\n    referenceRef,\n    popperRef,\n    getPopperProps,\n    getArrowProps,\n    getArrowInnerProps,\n    getReferenceProps,\n  }\n}\n\nfunction getArrowStyle(props: any) {\n  const { size, shadowColor, bg, style } = props\n  const computedStyle = { ...style, position: \"absolute\" }\n  if (size) {\n    computedStyle[\"--popper-arrow-size\"] = size\n  }\n  if (shadowColor) {\n    computedStyle[\"--popper-arrow-shadow-color\"] = shadowColor\n  }\n  if (bg) {\n    computedStyle[\"--popper-arrow-bg\"] = bg\n  }\n  return computedStyle\n}\n\nexport type UsePopperReturn = ReturnType<typeof usePopper>\n", "import { useCallbackRef } from \"@chakra-ui/react-use-callback-ref\"\nimport React, { useCallback, useState, useId } from \"react\"\n\nexport interface UseDisclosureProps {\n  isOpen?: boolean\n  defaultIsOpen?: boolean\n  onClose?(): void\n  onOpen?(): void\n  id?: string\n}\n\ntype HTMLProps = React.HTMLAttributes<HTMLElement>\n\n/**\n * `useDisclosure` is a custom hook used to help handle common open, close, or toggle scenarios.\n * It can be used to control feedback component such as `Modal`, `AlertDialog`, `Drawer`, etc.\n *\n * @see Docs https://chakra-ui.com/docs/hooks/use-disclosure\n */\nexport function useDisclosure(props: UseDisclosureProps = {}) {\n  const {\n    onClose: onCloseProp,\n    onOpen: onOpenProp,\n    isOpen: isOpenProp,\n    id: idProp,\n  } = props\n\n  const handleOpen = useCallbackRef(onOpenProp)\n  const handleClose = useCallbackRef(onCloseProp)\n\n  const [isOpenState, setIsOpen] = useState(props.defaultIsOpen || false)\n\n  const isOpen = isOpenProp !== undefined ? isOpenProp : isOpenState\n\n  const isControlled = isOpenProp !== undefined\n\n  const uid = useId()\n  const id = idProp ?? `disclosure-${uid}`\n\n  const onClose = useCallback(() => {\n    if (!isControlled) {\n      setIsOpen(false)\n    }\n    handleClose?.()\n  }, [isControlled, handleClose])\n\n  const onOpen = useCallback(() => {\n    if (!isControlled) {\n      setIsOpen(true)\n    }\n    handleOpen?.()\n  }, [isControlled, handleOpen])\n\n  const onToggle = useCallback(() => {\n    if (isOpen) {\n      onClose()\n    } else {\n      onOpen()\n    }\n  }, [isOpen, onOpen, onClose])\n\n  function getButtonProps(props: HTMLProps = {}): HTMLProps {\n    return {\n      ...props,\n      \"aria-expanded\": isOpen,\n      \"aria-controls\": id,\n      onClick(event) {\n        props.onClick?.(event)\n        onToggle()\n      },\n    }\n  }\n\n  function getDisclosureProps(props: HTMLProps = {}): HTMLProps {\n    return {\n      ...props,\n      hidden: !isOpen,\n      id,\n    }\n  }\n\n  return {\n    isOpen,\n    onOpen,\n    onClose,\n    onToggle,\n    isControlled,\n    getButtonProps,\n    getDisclosureProps,\n  }\n}\n\nexport type UseDisclosureReturn = ReturnType<typeof useDisclosure>\n", "import { useEffect, useRef } from \"react\"\nimport { useCallbackRef } from \"@chakra-ui/react-use-callback-ref\"\n\nexport interface UseOutsideClickProps {\n  /**\n   * Whether the hook is enabled\n   */\n  enabled?: boolean\n  /**\n   * The reference to a DOM element.\n   */\n  ref: React.RefObject<HTMLElement>\n  /**\n   * Function invoked when a click is triggered outside the referenced element.\n   */\n  handler?: (e: Event) => void\n}\n\n/**\n * Example, used in components like Dialogs and Popovers, so they can close\n * when a user clicks outside them.\n */\nexport function useOutsideClick(props: UseOutsideClickProps) {\n  const { ref, handler, enabled = true } = props\n  const savedHandler = useCallbackRef(handler)\n\n  const stateRef = useRef({\n    isPointerDown: false,\n    ignoreEmulatedMouseEvents: false,\n  })\n\n  const state = stateRef.current\n\n  useEffect(() => {\n    if (!enabled) return\n    const onPointerDown: any = (e: PointerEvent) => {\n      if (isValidEvent(e, ref)) {\n        state.isPointerDown = true\n      }\n    }\n\n    const onMouseUp: any = (event: MouseEvent) => {\n      if (state.ignoreEmulatedMouseEvents) {\n        state.ignoreEmulatedMouseEvents = false\n        return\n      }\n\n      if (state.isPointerDown && handler && isValidEvent(event, ref)) {\n        state.isPointerDown = false\n        savedHandler(event)\n      }\n    }\n\n    const onTouchEnd = (event: TouchEvent) => {\n      state.ignoreEmulatedMouseEvents = true\n      if (handler && state.isPointerDown && isValidEvent(event, ref)) {\n        state.isPointerDown = false\n        savedHandler(event)\n      }\n    }\n\n    const doc = getOwnerDocument(ref.current)\n    doc.addEventListener(\"mousedown\", onPointerDown, true)\n    doc.addEventListener(\"mouseup\", onMouseUp, true)\n    doc.addEventListener(\"touchstart\", onPointerDown, true)\n    doc.addEventListener(\"touchend\", onTouchEnd, true)\n\n    return () => {\n      doc.removeEventListener(\"mousedown\", onPointerDown, true)\n      doc.removeEventListener(\"mouseup\", onMouseUp, true)\n      doc.removeEventListener(\"touchstart\", onPointerDown, true)\n      doc.removeEventListener(\"touchend\", onTouchEnd, true)\n    }\n  }, [handler, ref, savedHandler, state, enabled])\n}\n\nfunction isValidEvent(event: Event, ref: React.RefObject<HTMLElement>) {\n  const target = event.target as HTMLElement\n\n  if (target) {\n    const doc = getOwnerDocument(target)\n    if (!doc.contains(target)) return false\n  }\n\n  return !ref.current?.contains(target)\n}\n\nfunction getOwnerDocument(node?: Element | null): Document {\n  return node?.ownerDocument ?? document\n}\n", "import { useEffect, useState } from \"react\"\nimport { useEventListener } from \"@chakra-ui/react-use-event-listener\"\nimport { getOwnerWindow } from \"@chakra-ui/dom-utils\"\nexport type UseAnimationStateProps = {\n  isOpen: boolean\n  ref: React.RefObject<HTMLElement>\n}\n\nexport function useAnimationState(props: UseAnimationStateProps) {\n  const { isOpen, ref } = props\n\n  const [mounted, setMounted] = useState(isOpen)\n  const [once, setOnce] = useState(false)\n\n  useEffect(() => {\n    if (!once) {\n      setMounted(isOpen)\n      setOnce(true)\n    }\n  }, [isOpen, once, mounted])\n\n  useEventListener(\n    () => ref.current,\n    \"animationend\",\n    () => {\n      setMounted(isOpen)\n    },\n  )\n\n  const hidden = isOpen ? false : !mounted\n\n  return {\n    present: !hidden,\n    onComplete() {\n      const win = getOwnerWindow(ref.current)\n      const evt = new win.CustomEvent(\"animationend\", { bubbles: true })\n      ref.current?.dispatchEvent(evt)\n    },\n  }\n}\n", "import { useMemo, useState } from \"react\"\nimport { useCallbackRef } from \"@chakra-ui/react-use-callback-ref\"\n\n/**\n * Given a prop value and state value, the useControllableProp hook is used to determine whether a component is controlled or uncontrolled, and also returns the computed value.\n *\n * @see Docs https://chakra-ui.com/docs/hooks/use-controllable#usecontrollableprop\n */\nexport function useControllableProp<T>(prop: T | undefined, state: T) {\n  const controlled = typeof prop !== \"undefined\"\n  const value = controlled ? prop : state\n  return useMemo<[boolean, T]>(() => [controlled, value], [controlled, value])\n}\n\nexport interface UseControllableStateProps<T> {\n  value?: T\n  defaultValue?: T | (() => T)\n  onChange?: (value: T) => void\n  shouldUpdate?: (prev: T, next: T) => boolean\n}\n\n/**\n * The `useControllableState` hook returns the state and function that updates the state, just like React.useState does.\n *\n * @see Docs https://chakra-ui.com/docs/hooks/use-controllable#usecontrollablestate\n */\nexport function useControllableState<T>(props: UseControllableStateProps<T>) {\n  const {\n    value: valueProp,\n    defaultValue,\n    onChange,\n    shouldUpdate = (prev, next) => prev !== next,\n  } = props\n\n  const onChangeProp = useCallbackRef(onChange)\n  const shouldUpdateProp = useCallbackRef(shouldUpdate)\n\n  const [uncontrolledState, setUncontrolledState] = useState(defaultValue as T)\n  const controlled = valueProp !== undefined\n  const value = controlled ? valueProp : uncontrolledState\n\n  const setValue = useCallbackRef(\n    (next: React.SetStateAction<T>) => {\n      const setter = next as (prevState?: T) => T\n      const nextValue = typeof next === \"function\" ? setter(value) : next\n\n      if (!shouldUpdateProp(value, nextValue)) {\n        return\n      }\n\n      if (!controlled) {\n        setUncontrolledState(nextValue)\n      }\n\n      onChangeProp(nextValue)\n    },\n    [controlled, onChangeProp, value, shouldUpdateProp],\n  )\n\n  return [value, setValue] as [T, React.Dispatch<React.SetStateAction<T>>]\n}\n", "// src/index.ts\nfunction lazyDisclosure(options) {\n  const { wasSelected, enabled, isSelected, mode = \"unmount\" } = options;\n  if (!enabled)\n    return true;\n  if (isSelected)\n    return true;\n  if (mode === \"keepMounted\" && wasSelected)\n    return true;\n  return false;\n}\nexport {\n  lazyDisclosure\n};\n", "import { useClickable } from \"@chakra-ui/clickable\"\nimport { createDescendantContext } from \"@chakra-ui/descendant\"\nimport { useFocusOnHide } from \"@chakra-ui/react-use-focus-effect\"\nimport { usePopper, UsePopperProps } from \"@chakra-ui/popper\"\nimport {\n  useDisclosure,\n  UseDisclosureProps,\n} from \"@chakra-ui/react-use-disclosure\"\nimport { useOutsideClick } from \"@chakra-ui/react-use-outside-click\"\nimport { useAnimationState } from \"@chakra-ui/react-use-animation-state\"\nimport { createContext } from \"@chakra-ui/react-context\"\nimport { getValidChildren } from \"@chakra-ui/react-children-utils\"\nimport { useControllableState } from \"@chakra-ui/react-use-controllable-state\"\nimport { useUpdateEffect } from \"@chakra-ui/react-use-update-effect\"\nimport { mergeRefs } from \"@chakra-ui/react-use-merge-refs\"\nimport { dataAttr, callAllHandlers } from \"@chakra-ui/shared-utils\"\nimport { lazyDisclosure, LazyMode } from \"@chakra-ui/lazy-utils\"\n\nimport React, {\n  cloneElement,\n  useCallback,\n  useRef,\n  useState,\n  useId,\n  useMemo,\n  useEffect,\n} from \"react\"\nimport { useShortcut } from \"./use-shortcut\"\nimport { getNextItemFromSearch } from \"./get-next-item-from-search\"\n\n/* -------------------------------------------------------------------------------------------------\n * Create context to track descendants and their indices\n * -----------------------------------------------------------------------------------------------*/\n\nexport const [\n  MenuDescendantsProvider,\n  useMenuDescendantsContext,\n  useMenuDescendants,\n  useMenuDescendant,\n] = createDescendantContext<HTMLElement>()\n\n/* -------------------------------------------------------------------------------------------------\n * Create context to track menu state and logic\n * -----------------------------------------------------------------------------------------------*/\n\nexport const [MenuProvider, useMenuContext] = createContext<\n  Omit<UseMenuReturn, \"descendants\">\n>({\n  strict: false,\n  name: \"MenuContext\",\n})\n\n/* -------------------------------------------------------------------------------------------------\n * useMenu hook\n * -----------------------------------------------------------------------------------------------*/\n\nexport interface UseMenuProps\n  extends Omit<UsePopperProps, \"enabled\">,\n    UseDisclosureProps {\n  /**\n   * The `ref` of the element that should receive focus when the popover opens.\n   */\n  initialFocusRef?: React.RefObject<{ focus(): void }>\n  /**\n   * If `true`, the menu will close when a menu item is\n   * clicked\n   *\n   * @default true\n   */\n  closeOnSelect?: boolean\n  /**\n   * If `true`, the menu will close when you click outside\n   * the menu list\n   *\n   * @default true\n   */\n  closeOnBlur?: boolean\n  /**\n   * If `true`, the first enabled menu item will receive focus and be selected\n   * when the menu opens.\n   *\n   * @default true\n   */\n  autoSelect?: boolean\n  /**\n   * Performance 🚀:\n   * If `true`, the MenuItem rendering will be deferred\n   * until the menu is open.\n   *\n   * @default false\n   */\n  isLazy?: boolean\n  /**\n   * Performance 🚀:\n   * The lazy behavior of menu's content when not visible.\n   * Only works when `isLazy={true}`\n   *\n   * - \"unmount\": The menu's content is always unmounted when not open.\n   * - \"keepMounted\": The menu's content initially unmounted,\n   * but stays mounted when menu is open.\n   *\n   * @default \"unmount\"\n   */\n  lazyBehavior?: LazyMode\n  /**\n   * If `rtl`, proper placement positions will be flipped i.e. 'top-right' will\n   * become 'top-left' and vice-verse\n   */\n  direction?: \"ltr\" | \"rtl\"\n  /*\n   * If `true`, the menu will be positioned when it mounts\n   * (even if it's not open).\n   *\n   * Note 🚨: We don't recommend using this in a menu/popover intensive UI or page\n   * as it might affect scrolling performance.\n   *\n   * @default false\n   */\n  computePositionOnMount?: boolean\n}\n\nfunction useIds(idProp?: string, ...prefixes: string[]) {\n  const reactId = useId()\n  const id = idProp || reactId\n  return useMemo(() => {\n    return prefixes.map((prefix) => `${prefix}-${id}`)\n  }, [id, prefixes])\n}\n\nfunction getOwnerDocument(node?: Element | null): Document {\n  return node?.ownerDocument ?? document\n}\n\nfunction isActiveElement(element: HTMLElement) {\n  const doc = getOwnerDocument(element)\n  return doc.activeElement === (element as HTMLElement)\n}\n\n/**\n * React Hook to manage a menu\n *\n * It provides the logic and will be used with react context\n * to propagate its return value to all children\n */\nexport function useMenu(props: UseMenuProps = {}) {\n  const {\n    id,\n    closeOnSelect = true,\n    closeOnBlur = true,\n    initialFocusRef,\n    autoSelect = true,\n    isLazy,\n    isOpen: isOpenProp,\n    defaultIsOpen,\n    onClose: onCloseProp,\n    onOpen: onOpenProp,\n    placement = \"bottom-start\",\n    lazyBehavior = \"unmount\",\n    direction,\n    computePositionOnMount = false,\n    ...popperProps\n  } = props\n  /**\n   * Prepare the reference to the menu and disclosure\n   */\n  const menuRef = useRef<HTMLDivElement>(null)\n  const buttonRef = useRef<HTMLButtonElement>(null)\n\n  /**\n   * Context to register all menu item nodes\n   */\n  const descendants = useMenuDescendants()\n\n  const focusMenu = useCallback(() => {\n    requestAnimationFrame(() => {\n      menuRef.current?.focus({ preventScroll: false })\n    })\n  }, [])\n\n  const focusFirstItem = useCallback(() => {\n    const id = setTimeout(() => {\n      if (initialFocusRef) {\n        initialFocusRef.current?.focus()\n      } else {\n        const first = descendants.firstEnabled()\n        if (first) setFocusedIndex(first.index)\n      }\n    })\n    timeoutIds.current.add(id)\n  }, [descendants, initialFocusRef])\n\n  const focusLastItem = useCallback(() => {\n    const id = setTimeout(() => {\n      const last = descendants.lastEnabled()\n      if (last) setFocusedIndex(last.index)\n    })\n    timeoutIds.current.add(id)\n  }, [descendants])\n\n  const onOpenInternal = useCallback(() => {\n    onOpenProp?.()\n    if (autoSelect) {\n      focusFirstItem()\n    } else {\n      focusMenu()\n    }\n  }, [autoSelect, focusFirstItem, focusMenu, onOpenProp])\n\n  const { isOpen, onOpen, onClose, onToggle } = useDisclosure({\n    isOpen: isOpenProp,\n    defaultIsOpen,\n    onClose: onCloseProp,\n    onOpen: onOpenInternal,\n  })\n\n  useOutsideClick({\n    enabled: isOpen && closeOnBlur,\n    ref: menuRef,\n    handler: (event) => {\n      if (!buttonRef.current?.contains(event.target as HTMLElement)) {\n        onClose()\n      }\n    },\n  })\n\n  /**\n   * Add some popper.js for dynamic positioning\n   */\n  const popper: any = usePopper({\n    ...popperProps,\n    enabled: isOpen || computePositionOnMount,\n    placement,\n    direction,\n  })\n\n  const [focusedIndex, setFocusedIndex] = useState(-1)\n\n  /**\n   * Focus the button when we close the menu\n   */\n  useUpdateEffect(() => {\n    if (!isOpen) {\n      setFocusedIndex(-1)\n    }\n  }, [isOpen])\n\n  useFocusOnHide(menuRef, {\n    focusRef: buttonRef,\n    visible: isOpen,\n    shouldFocus: true,\n  })\n\n  const animationState = useAnimationState({ isOpen, ref: menuRef })\n\n  /**\n   * Generate unique ids for menu's list and button\n   */\n  const [buttonId, menuId] = useIds(id, `menu-button`, `menu-list`)\n\n  const openAndFocusMenu = useCallback(() => {\n    onOpen()\n    focusMenu()\n  }, [onOpen, focusMenu])\n\n  const timeoutIds = useRef<Set<any>>(new Set([]))\n\n  // clean up timeouts\n  useEffect(() => {\n    const ids = timeoutIds.current\n    return () => {\n      ids.forEach((id) => clearTimeout(id))\n      ids.clear()\n    }\n  }, [])\n\n  const openAndFocusFirstItem = useCallback(() => {\n    onOpen()\n    focusFirstItem()\n  }, [focusFirstItem, onOpen])\n\n  const openAndFocusLastItem = useCallback(() => {\n    onOpen()\n    focusLastItem()\n  }, [onOpen, focusLastItem])\n\n  const refocus = useCallback(() => {\n    const doc = getOwnerDocument(menuRef.current)\n    const hasFocusWithin = menuRef.current?.contains(doc.activeElement)\n    const shouldRefocus = isOpen && !hasFocusWithin\n\n    if (!shouldRefocus) return\n\n    const node = descendants.item(focusedIndex)?.node\n    node?.focus({ preventScroll: true })\n  }, [isOpen, focusedIndex, descendants])\n\n  /**\n   * Track the animation frame which is scheduled to focus\n   * a menu item, so it can be cancelled if another item\n   * is focused before the animation executes. This prevents\n   * infinite rerenders.\n   */\n  const rafId = useRef<number | null>(null)\n\n  return {\n    openAndFocusMenu,\n    openAndFocusFirstItem,\n    openAndFocusLastItem,\n    onTransitionEnd: refocus,\n    unstable__animationState: animationState,\n    descendants,\n    popper,\n    buttonId,\n    menuId,\n    forceUpdate: popper.forceUpdate,\n    orientation: \"vertical\",\n    isOpen,\n    onToggle,\n    onOpen,\n    onClose,\n    menuRef,\n    buttonRef,\n    focusedIndex,\n    closeOnSelect,\n    closeOnBlur,\n    autoSelect,\n    setFocusedIndex,\n    isLazy,\n    lazyBehavior,\n    initialFocusRef,\n    rafId,\n  }\n}\n\nexport interface UseMenuReturn extends ReturnType<typeof useMenu> {}\n\n/* -------------------------------------------------------------------------------------------------\n * useMenuButton hook\n * -----------------------------------------------------------------------------------------------*/\nexport interface UseMenuButtonProps\n  extends Omit<React.HTMLAttributes<Element>, \"color\"> {}\n\n/**\n * React Hook to manage a menu button.\n *\n * The assumption here is that the `useMenu` hook is used\n * in a component higher up the tree, and its return value\n * is passed as `context` to this hook.\n */\nexport function useMenuButton(\n  props: UseMenuButtonProps = {},\n  externalRef: React.Ref<any> = null,\n) {\n  const menu = useMenuContext()\n\n  const { onToggle, popper, openAndFocusFirstItem, openAndFocusLastItem } = menu\n\n  const onKeyDown = useCallback(\n    (event: React.KeyboardEvent) => {\n      const eventKey = event.key\n      const keyMap: Record<string, React.KeyboardEventHandler> = {\n        Enter: openAndFocusFirstItem,\n        ArrowDown: openAndFocusFirstItem,\n        ArrowUp: openAndFocusLastItem,\n      }\n\n      const action = keyMap[eventKey]\n\n      if (action) {\n        event.preventDefault()\n        event.stopPropagation()\n        action(event)\n      }\n    },\n    [openAndFocusFirstItem, openAndFocusLastItem],\n  )\n\n  return {\n    ...props,\n    ref: mergeRefs(menu.buttonRef, externalRef, popper.referenceRef),\n    id: menu.buttonId,\n    \"data-active\": dataAttr(menu.isOpen),\n    \"aria-expanded\": menu.isOpen,\n    \"aria-haspopup\": \"menu\" as React.AriaAttributes[\"aria-haspopup\"],\n    \"aria-controls\": menu.menuId,\n    onClick: callAllHandlers(props.onClick, onToggle),\n    onKeyDown: callAllHandlers(props.onKeyDown, onKeyDown),\n  }\n}\n\nfunction isTargetMenuItem(target: EventTarget | null) {\n  // this will catch `menuitem`, `menuitemradio`, `menuitemcheckbox`\n  return (\n    isHTMLElement(target) &&\n    !!target?.getAttribute(\"role\")?.startsWith(\"menuitem\")\n  )\n}\n\n/* -------------------------------------------------------------------------------------------------\n * useMenuList\n * -----------------------------------------------------------------------------------------------*/\n\nexport interface UseMenuListProps\n  extends Omit<React.HTMLAttributes<Element>, \"color\"> {}\n\n/**\n * React Hook to manage a menu list.\n *\n * The assumption here is that the `useMenu` hook is used\n * in a component higher up the tree, and its return value\n * is passed as `context` to this hook.\n */\nexport function useMenuList(\n  props: UseMenuListProps = {},\n  ref: React.Ref<any> = null,\n): React.HTMLAttributes<HTMLElement> & React.RefAttributes<HTMLElement> {\n  const menu = useMenuContext()\n\n  if (!menu) {\n    throw new Error(\n      `useMenuContext: context is undefined. Seems you forgot to wrap component within <Menu>`,\n    )\n  }\n\n  const {\n    focusedIndex,\n    setFocusedIndex,\n    menuRef,\n    isOpen,\n    onClose,\n    menuId,\n    isLazy,\n    lazyBehavior,\n    unstable__animationState: animated,\n  } = menu\n\n  const descendants = useMenuDescendantsContext()\n\n  /**\n   * Hook that creates a keydown event handler that listens\n   * to printable keyboard character press\n   */\n  const createTypeaheadHandler = useShortcut({\n    preventDefault: (event) =>\n      event.key !== \" \" && isTargetMenuItem(event.target),\n  })\n\n  const onKeyDown = useCallback(\n    (event: React.KeyboardEvent) => {\n      // ignore events bubbles from portal children\n      if (!event.currentTarget.contains(event.target as Element)) return\n\n      const eventKey = event.key\n\n      const keyMap: Record<string, React.KeyboardEventHandler> = {\n        Tab: (event) => event.preventDefault(),\n        Escape: onClose,\n        ArrowDown: () => {\n          const next = descendants.nextEnabled(focusedIndex)\n          if (next) setFocusedIndex(next.index)\n        },\n        ArrowUp: () => {\n          const prev = descendants.prevEnabled(focusedIndex)\n          if (prev) setFocusedIndex(prev.index)\n        },\n      }\n\n      const fn = keyMap[eventKey]\n\n      if (fn) {\n        event.preventDefault()\n        fn(event)\n        return\n      }\n\n      /**\n       * Typeahead: Based on current character pressed,\n       * find the next item to be selected\n       */\n      const onTypeahead = createTypeaheadHandler((character) => {\n        const nextItem = getNextItemFromSearch(\n          descendants.values(),\n          character,\n          (item) => item?.node?.textContent ?? \"\",\n          descendants.item(focusedIndex),\n        )\n        if (nextItem) {\n          const index = descendants.indexOf(nextItem.node)\n          setFocusedIndex(index)\n        }\n      })\n\n      if (isTargetMenuItem(event.target)) {\n        onTypeahead(event)\n      }\n    },\n    [\n      descendants,\n      focusedIndex,\n      createTypeaheadHandler,\n      onClose,\n      setFocusedIndex,\n    ],\n  )\n\n  const hasBeenOpened = useRef(false)\n  if (isOpen) {\n    hasBeenOpened.current = true\n  }\n\n  const shouldRenderChildren = lazyDisclosure({\n    wasSelected: hasBeenOpened.current,\n    enabled: isLazy,\n    mode: lazyBehavior,\n    isSelected: animated.present,\n  })\n\n  return {\n    ...props,\n    ref: mergeRefs(menuRef, ref),\n    children: shouldRenderChildren ? props.children : null,\n    tabIndex: -1,\n    role: \"menu\",\n    id: menuId,\n    style: {\n      ...props.style,\n      transformOrigin: \"var(--popper-transform-origin)\",\n    },\n    \"aria-orientation\": \"vertical\" as React.AriaAttributes[\"aria-orientation\"],\n    onKeyDown: callAllHandlers(props.onKeyDown, onKeyDown),\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * useMenuPosition: Composes usePopper to position the menu\n * -----------------------------------------------------------------------------------------------*/\n\nexport function useMenuPositioner(props: any = {}) {\n  const { popper, isOpen } = useMenuContext()\n  return popper.getPopperProps({\n    ...props,\n    style: {\n      visibility: isOpen ? \"visible\" : \"hidden\",\n      ...props.style,\n    },\n  })\n}\n\n/* -------------------------------------------------------------------------------------------------\n * useMenuItem: Hook for each menu item within the menu list.\n   We also use it in `useMenuItemOption`\n * -----------------------------------------------------------------------------------------------*/\n\nexport interface UseMenuItemProps\n  extends Omit<React.HTMLAttributes<Element>, \"color\" | \"disabled\"> {\n  /**\n   * If `true`, the menuitem will be disabled\n   */\n  isDisabled?: boolean\n  /**\n   * If `true` and the menuitem is disabled, it'll\n   * remain keyboard-focusable\n   */\n  isFocusable?: boolean\n  /**\n   * Overrides the parent menu's `closeOnSelect` prop.\n   */\n  closeOnSelect?: boolean\n  /**\n   * The type of the menuitem.\n   */\n  type?: React.ButtonHTMLAttributes<HTMLButtonElement>[\"type\"]\n}\n\nexport function useMenuItem(\n  props: UseMenuItemProps = {},\n  externalRef: React.Ref<any> = null,\n) {\n  const {\n    onMouseEnter: onMouseEnterProp,\n    onMouseMove: onMouseMoveProp,\n    onMouseLeave: onMouseLeaveProp,\n    onClick: onClickProp,\n    onFocus: onFocusProp,\n    isDisabled,\n    isFocusable,\n    closeOnSelect,\n    type: typeProp,\n    ...htmlProps\n  } = props\n\n  const menu = useMenuContext()\n\n  const {\n    setFocusedIndex,\n    focusedIndex,\n    closeOnSelect: menuCloseOnSelect,\n    onClose,\n    menuRef,\n    isOpen,\n    menuId,\n    rafId,\n  } = menu\n\n  const ref = useRef<HTMLDivElement>(null)\n  const id = `${menuId}-menuitem-${useId()}`\n\n  /**\n   * Register the menuitem's node into the domContext\n   */\n  const { index, register } = useMenuDescendant({\n    disabled: isDisabled && !isFocusable,\n  })\n\n  const onMouseEnter = useCallback(\n    (event: any) => {\n      onMouseEnterProp?.(event)\n      if (isDisabled) return\n      setFocusedIndex(index)\n    },\n    [setFocusedIndex, index, isDisabled, onMouseEnterProp],\n  )\n\n  const onMouseMove = useCallback(\n    (event: any) => {\n      onMouseMoveProp?.(event)\n      if (ref.current && !isActiveElement(ref.current)) {\n        onMouseEnter(event)\n      }\n    },\n    [onMouseEnter, onMouseMoveProp],\n  )\n\n  const onMouseLeave = useCallback(\n    (event: any) => {\n      onMouseLeaveProp?.(event)\n      if (isDisabled) return\n      setFocusedIndex(-1)\n    },\n    [setFocusedIndex, isDisabled, onMouseLeaveProp],\n  )\n\n  const onClick = useCallback(\n    (event: React.MouseEvent) => {\n      onClickProp?.(event)\n      if (!isTargetMenuItem(event.currentTarget)) return\n      /**\n       * Close menu and parent menus, allowing the MenuItem\n       * to override its parent menu's `closeOnSelect` prop.\n       */\n      if (closeOnSelect ?? menuCloseOnSelect) {\n        onClose()\n      }\n    },\n    [onClose, onClickProp, menuCloseOnSelect, closeOnSelect],\n  )\n\n  const onFocus = useCallback(\n    (event: React.FocusEvent) => {\n      onFocusProp?.(event)\n      setFocusedIndex(index)\n    },\n    [setFocusedIndex, onFocusProp, index],\n  )\n\n  const isFocused = index === focusedIndex\n\n  const trulyDisabled = isDisabled && !isFocusable\n\n  useUpdateEffect(() => {\n    if (!isOpen) return\n    if (isFocused && !trulyDisabled && ref.current) {\n      // Cancel any pending animations\n      if (rafId.current) {\n        cancelAnimationFrame(rafId.current)\n      }\n      rafId.current = requestAnimationFrame(() => {\n        ref.current?.focus({ preventScroll: true })\n        rafId.current = null\n      })\n    } else if (menuRef.current && !isActiveElement(menuRef.current)) {\n      menuRef.current.focus({ preventScroll: true })\n    }\n\n    return () => {\n      if (rafId.current) {\n        cancelAnimationFrame(rafId.current)\n      }\n    }\n  }, [isFocused, trulyDisabled, menuRef, isOpen])\n\n  const clickableProps = useClickable({\n    onClick,\n    onFocus,\n    onMouseEnter,\n    onMouseMove,\n    onMouseLeave,\n    ref: mergeRefs(register, ref, externalRef),\n    isDisabled,\n    isFocusable,\n  })\n\n  return {\n    ...htmlProps,\n    ...clickableProps,\n    type: typeProp ?? (clickableProps as any).type,\n    id,\n    role: \"menuitem\",\n    tabIndex: isFocused ? 0 : -1,\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * useMenuOption: Composes useMenuItem to provide a selectable/checkable menu item\n * -----------------------------------------------------------------------------------------------*/\n\nexport interface UseMenuOptionOptions {\n  value?: string\n  isChecked?: boolean\n  type?: \"radio\" | \"checkbox\"\n  children?: React.ReactNode\n}\n\nexport interface UseMenuOptionProps\n  extends Omit<UseMenuItemProps, \"type\">,\n    UseMenuOptionOptions {}\n\nexport function useMenuOption(\n  props: UseMenuOptionProps = {},\n  ref: React.Ref<any> = null,\n) {\n  const { type = \"radio\", isChecked, ...rest } = props\n  const ownProps = useMenuItem(rest, ref)\n  return {\n    ...ownProps,\n    role: `menuitem${type}`,\n    \"aria-checked\": isChecked as React.AriaAttributes[\"aria-checked\"],\n  }\n}\n\n/* -------------------------------------------------------------------------------------------------\n * useMenuOptionGroup: Manages the state of multiple selectable menuitem or menu option\n * -----------------------------------------------------------------------------------------------*/\n\nexport interface UseMenuOptionGroupProps {\n  value?: string | string[]\n  defaultValue?: string | string[]\n  type?: \"radio\" | \"checkbox\"\n  onChange?: (value: string | string[]) => void\n  children?: React.ReactNode\n}\n\nexport function useMenuOptionGroup(props: UseMenuOptionGroupProps = {}) {\n  const {\n    children,\n    type = \"radio\",\n    value: valueProp,\n    defaultValue,\n    onChange: onChangeProp,\n    ...htmlProps\n  } = props\n\n  const isRadio = type === \"radio\"\n\n  const fallback = isRadio ? \"\" : []\n\n  const [value, setValue] = useControllableState({\n    defaultValue: defaultValue ?? fallback,\n    value: valueProp,\n    onChange: onChangeProp,\n  })\n\n  const onChange = useCallback(\n    (selectedValue: string) => {\n      if (type === \"radio\" && typeof value === \"string\") {\n        setValue(selectedValue)\n      }\n\n      if (type === \"checkbox\" && Array.isArray(value)) {\n        const nextValue = value.includes(selectedValue)\n          ? value.filter((item) => item !== selectedValue)\n          : value.concat(selectedValue)\n\n        setValue(nextValue)\n      }\n    },\n    [value, setValue, type],\n  )\n\n  const validChildren = getValidChildren(children)\n\n  const clones = validChildren.map((child) => {\n    /**\n     * We've added an internal `id` to each `MenuItemOption`,\n     * let's use that for type-checking.\n     *\n     * We can't rely on displayName or the element's type since\n     * they can be changed by the user.\n     */\n    if ((child.type as any).id !== \"MenuItemOption\") return child\n\n    const onClick = (event: MouseEvent) => {\n      onChange(child.props.value)\n      child.props.onClick?.(event)\n    }\n\n    const isChecked =\n      type === \"radio\"\n        ? child.props.value === value\n        : value.includes(child.props.value)\n\n    return cloneElement(child, {\n      type,\n      onClick,\n      isChecked,\n    })\n  })\n\n  return {\n    ...htmlProps,\n    children: clones,\n  }\n}\n\nexport function useMenuState() {\n  const { isOpen, onClose } = useMenuContext()\n  return { isOpen, onClose }\n}\n\nfunction isHTMLElement(el: any): el is HTMLElement {\n  if (!isElement(el)) return false\n  const win = el.ownerDocument.defaultView ?? window\n  return el instanceof win.HTMLElement\n}\n\nfunction isElement(el: any): el is Element {\n  return (\n    el != null &&\n    typeof el == \"object\" &&\n    \"nodeType\" in el &&\n    el.nodeType === Node.ELEMENT_NODE\n  )\n}\n", "import { createContext } from \"@chakra-ui/react-context\"\nimport {\n  omitThemingProps,\n  SystemStyleObject,\n  ThemingProps,\n  useMultiStyleConfig,\n  useTheme,\n} from \"@chakra-ui/system\"\nimport { runIfFn } from \"@chakra-ui/shared-utils\"\nimport { useMemo } from \"react\"\nimport {\n  MenuDescendantsProvider,\n  MenuProvider,\n  useMenu,\n  UseMenuProps,\n} from \"./use-menu\"\n\nconst [MenuStylesProvider, useMenuStyles] = createContext<\n  Record<string, SystemStyleObject>\n>({\n  name: `MenuStylesContext`,\n  errorMessage: `useMenuStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Menu />\" `,\n})\n\nexport { useMenuStyles }\n\ntype MaybeRenderProp<P> = React.ReactNode | ((props: P) => React.ReactNode)\n\nexport interface MenuProps extends UseMenuProps, ThemingProps<\"Menu\"> {\n  children: MaybeRenderProp<{\n    isOpen: boolean\n    onClose: () => void\n    forceUpdate: (() => void) | undefined\n  }>\n}\n\n/**\n * Menu provides context, state, and focus management\n * to its sub-components. It doesn't render any DOM node.\n *\n * @see Docs https://chakra-ui.com/docs/components/menu\n */\nexport const Menu: React.FC<MenuProps> = (props) => {\n  const { children } = props\n\n  const styles = useMultiStyleConfig(\"Menu\", props)\n  const ownProps = omitThemingProps(props)\n  const { direction } = useTheme()\n  const { descendants, ...ctx } = useMenu({ ...ownProps, direction })\n  const context = useMemo(() => ctx, [ctx])\n\n  const { isOpen, onClose, forceUpdate } = context\n\n  return (\n    <MenuDescendantsProvider value={descendants}>\n      <MenuProvider value={context}>\n        <MenuStylesProvider value={styles}>\n          {runIfFn(children, { isOpen, onClose, forceUpdate })}\n        </MenuStylesProvider>\n      </MenuProvider>\n    </MenuDescendantsProvider>\n  )\n}\n\nMenu.displayName = \"Menu\"\n", "import { HTMLChakraProps, chakra, forwardRef } from \"@chakra-ui/system\"\nimport { useMenuStyles } from \"./menu\"\n\nexport interface MenuCommandProps extends HTMLChakraProps<\"span\"> {}\n\nexport const MenuCommand = forwardRef<MenuCommandProps, \"span\">(\n  (props, ref) => {\n    const styles = useMenuStyles()\n    return (\n      <chakra.span\n        ref={ref}\n        {...props}\n        __css={styles.command}\n        className=\"chakra-menu__command\"\n      />\n    )\n  },\n)\n\nMenuCommand.displayName = \"MenuCommand\"\n", "import { chakra, forwardRef, SystemStyleObject } from \"@chakra-ui/system\"\nimport { useMemo } from \"react\"\nimport { useMenuStyles } from \"./menu\"\nimport { StyledMenuItemProps } from \"./menu-item\"\n\nexport const StyledMenuItem = forwardRef<StyledMenuItemProps, \"button\">(\n  (props, ref) => {\n    const { type, ...rest } = props\n    const styles = useMenuStyles()\n\n    /**\n     * Given another component, use its type if present\n     * Else, use no type to avoid invalid html, e.g. <a type=\"button\" />\n     * Else, fall back to \"button\"\n     */\n    const btnType = rest.as || type ? type ?? undefined : \"button\"\n\n    const buttonStyles: SystemStyleObject = useMemo(\n      () => ({\n        textDecoration: \"none\",\n        color: \"inherit\",\n        userSelect: \"none\",\n        display: \"flex\",\n        width: \"100%\",\n        alignItems: \"center\",\n        textAlign: \"start\",\n        flex: \"0 0 auto\",\n        outline: 0,\n        ...styles.item,\n      }),\n      [styles.item],\n    )\n\n    return (\n      <chakra.button ref={ref} type={btnType} {...rest} __css={buttonStyles} />\n    )\n  },\n)\n", "import { HTMLChakraProps, chakra } from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nimport { Children, cloneElement, isValidElement } from \"react\"\nimport { useMenuStyles } from \"./menu\"\n\nexport const MenuIcon: React.FC<HTMLChakraProps<\"span\">> = (props) => {\n  const { className, children, ...rest } = props\n\n  const styles = useMenuStyles()\n\n  const child = Children.only(children)\n\n  const clone = isValidElement(child)\n    ? cloneElement<any>(child, {\n        focusable: \"false\",\n        \"aria-hidden\": true,\n        className: cx(\"chakra-menu__icon\", child.props.className),\n      })\n    : null\n\n  const _className = cx(\"chakra-menu__icon-wrapper\", className)\n\n  return (\n    <chakra.span className={_className} {...rest} __css={styles.icon}>\n      {clone}\n    </chakra.span>\n  )\n}\n\nMenuIcon.displayName = \"MenuIcon\"\n", "import { forwardRef, HTMLChakraProps, SystemProps } from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nimport { MenuCommand } from \"./menu-command\"\nimport { MenuIcon } from \"./menu-icon\"\nimport { StyledMenuItem } from \"./styled-menu-item\"\nimport { useMenuItem, UseMenuItemProps } from \"./use-menu\"\n\nexport interface StyledMenuItemProps extends HTMLChakraProps<\"button\"> {}\n\ninterface MenuItemOptions\n  extends Pick<\n    UseMenuItemProps,\n    \"isDisabled\" | \"isFocusable\" | \"closeOnSelect\"\n  > {\n  /**\n   * The icon to render before the menu item's label.\n   * @type React.ReactElement\n   */\n  icon?: React.ReactElement\n  /**\n   * The spacing between the icon and menu item's label.\n   * @type SystemProps[\"mr\"]\n   */\n  iconSpacing?: SystemProps[\"mr\"]\n  /**\n   * Right-aligned label text content, useful for displaying hotkeys.\n   */\n  command?: string\n  /**\n   * The spacing between the command and menu item's label.\n   * @type SystemProps[\"ml\"]\n   */\n  commandSpacing?: SystemProps[\"ml\"]\n}\n\ntype HTMLAttributes = React.HTMLAttributes<HTMLElement>\n\n/**\n * Use prop `isDisabled` instead\n */\ntype IsDisabledProps = \"disabled\" | \"aria-disabled\"\n\nexport interface MenuItemProps\n  extends Omit<HTMLChakraProps<\"button\">, IsDisabledProps>,\n    MenuItemOptions {}\n\nexport const MenuItem = forwardRef<MenuItemProps, \"button\">((props, ref) => {\n  const {\n    icon,\n    iconSpacing = \"0.75rem\",\n    command,\n    commandSpacing = \"0.75rem\",\n    children,\n    ...rest\n  } = props\n\n  const menuitemProps = useMenuItem(rest, ref) as HTMLAttributes\n\n  const shouldWrap = icon || command\n\n  const _children = shouldWrap ? (\n    <span style={{ pointerEvents: \"none\", flex: 1 }}>{children}</span>\n  ) : (\n    children\n  )\n\n  return (\n    <StyledMenuItem\n      {...menuitemProps}\n      className={cx(\"chakra-menu__menuitem\", menuitemProps.className)}\n    >\n      {icon && (\n        <MenuIcon fontSize=\"0.8em\" marginEnd={iconSpacing}>\n          {icon}\n        </MenuIcon>\n      )}\n      {_children}\n      {command && (\n        <MenuCommand marginStart={commandSpacing}>{command}</MenuCommand>\n      )}\n    </StyledMenuItem>\n  )\n})\n\nMenuItem.displayName = \"MenuItem\"\n", "import { callAll, cx } from \"@chakra-ui/shared-utils\"\nimport { chakra, forwardRef, HTMLChakraProps } from \"@chakra-ui/system\"\n\nimport { HTMLMotionProps, motion, Variants } from \"framer-motion\"\nimport { useMenuStyles } from \"./menu\"\nimport { useMenuContext, useMenuList, useMenuPositioner } from \"./use-menu\"\n\nexport interface MenuListProps extends HTMLChakraProps<\"div\"> {\n  /**\n   * Props for the root element that positions the menu.\n   */\n  rootProps?: HTMLChakraProps<\"div\">\n  /**\n   * The framer-motion props to animate the menu list\n   */\n  motionProps?: HTMLMotionProps<\"div\">\n}\n\nconst motionVariants: Variants = {\n  enter: {\n    visibility: \"visible\",\n    opacity: 1,\n    scale: 1,\n    transition: {\n      duration: 0.2,\n      ease: [0.4, 0, 0.2, 1],\n    },\n  },\n  exit: {\n    transitionEnd: {\n      visibility: \"hidden\",\n    },\n    opacity: 0,\n    scale: 0.8,\n    transition: {\n      duration: 0.1,\n      easings: \"easeOut\",\n    },\n  },\n}\n\nconst MenuTransition = chakra(motion.div)\n\nexport const MenuList = forwardRef<MenuListProps, \"div\">(function MenuList(\n  props,\n  ref,\n) {\n  const { rootProps, motionProps, ...rest } = props\n  const {\n    isOpen,\n    onTransitionEnd,\n    unstable__animationState: animated,\n  } = useMenuContext()\n\n  const listProps = useMenuList(rest, ref) as any\n  const positionerProps = useMenuPositioner(rootProps)\n\n  const styles = useMenuStyles()\n\n  return (\n    <chakra.div\n      {...positionerProps}\n      __css={{ zIndex: props.zIndex ?? styles.list?.zIndex }}\n    >\n      <MenuTransition\n        variants={motionVariants}\n        initial={false}\n        animate={isOpen ? \"enter\" : \"exit\"}\n        __css={{ outline: 0, ...styles.list }}\n        {...motionProps}\n        className={cx(\"chakra-menu__menu-list\", listProps.className)}\n        {...listProps}\n        onUpdate={onTransitionEnd}\n        onAnimationComplete={callAll(\n          animated.onComplete,\n          listProps.onAnimationComplete,\n        )}\n      />\n    </chakra.div>\n  )\n})\n\nMenuList.displayName = \"MenuList\"\n", "import { HTMLChakraProps, chakra, forwardRef } from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nimport { useMenuStyles } from \"./menu\"\n\nexport interface MenuGroupProps extends HTMLChakraProps<\"div\"> {}\n\nexport const MenuGroup = forwardRef<MenuGroupProps, \"div\">((props, ref) => {\n  const { title, children, className, ...rest } = props\n\n  const _className = cx(\"chakra-menu__group__title\", className)\n  const styles = useMenuStyles()\n\n  return (\n    <div ref={ref} className=\"chakra-menu__group\" role=\"group\">\n      {title && (\n        <chakra.p className={_className} {...rest} __css={styles.groupTitle}>\n          {title}\n        </chakra.p>\n      )}\n      {children}\n    </div>\n  )\n})\n\nMenuGroup.displayName = \"MenuGroup\"\n", "import { cx } from \"@chakra-ui/shared-utils\"\n\nimport { type MenuGroupProps, MenuGroup } from \"./menu-group\"\nimport { UseMenuOptionGroupProps, useMenuOptionGroup } from \"./use-menu\"\n\nexport interface MenuOptionGroupProps\n  extends UseMenuOptionGroupProps,\n    Omit<MenuGroupProps, \"value\" | \"defaultValue\" | \"onChange\"> {}\n\nexport const MenuOptionGroup: React.FC<MenuOptionGroupProps> = (props) => {\n  const { className, title, ...rest } = props\n  const ownProps = useMenuOptionGroup(rest)\n  return (\n    <MenuGroup\n      title={title}\n      className={cx(\"chakra-menu__option-group\", className)}\n      {...ownProps}\n    />\n  )\n}\n\nMenuOptionGroup.displayName = \"MenuOptionGroup\"\n", "import { forwardRef, HTMLChakraProps, chakra } from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nimport { useMenuStyles } from \"./menu\"\nimport { useMenuButton } from \"./use-menu\"\n\nexport interface MenuButtonProps extends HTMLChakraProps<\"button\"> {}\n\nconst StyledMenuButton = forwardRef<MenuButtonProps, \"button\">((props, ref) => {\n  const styles = useMenuStyles()\n  return (\n    <chakra.button\n      ref={ref}\n      {...props}\n      __css={{\n        display: \"inline-flex\",\n        appearance: \"none\",\n        alignItems: \"center\",\n        outline: 0,\n        ...styles.button,\n      }}\n    />\n  )\n})\n\n/**\n * The trigger for the menu list. Must be a direct child of `Menu`.\n *\n * @see WAI-ARIA https://www.w3.org/WAI/ARIA/apg/patterns/menubutton/\n */\nexport const MenuButton = forwardRef<MenuButtonProps, \"button\">(\n  (props, ref) => {\n    const { children, as: As, ...rest } = props\n\n    const buttonProps = useMenuButton(rest, ref)\n\n    const Element = As || StyledMenuButton\n\n    return (\n      <Element\n        {...buttonProps}\n        className={cx(\"chakra-menu__menu-button\", props.className)}\n      >\n        <chakra.span\n          __css={{ pointerEvents: \"none\", flex: \"1 1 auto\", minW: 0 }}\n        >\n          {props.children}\n        </chakra.span>\n      </Element>\n    )\n  },\n)\n\nMenuButton.displayName = \"MenuButton\"\n", "import { HTMLChakraProps, chakra } from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nimport { useMenuStyles } from \"./menu\"\n\nexport interface MenuDividerProps extends HTMLChakraProps<\"hr\"> {}\n\nexport const MenuDivider: React.FC<MenuDividerProps> = (props) => {\n  const { className, ...rest } = props\n  const styles = useMenuStyles()\n  return (\n    <chakra.hr\n      aria-orientation=\"horizontal\"\n      className={cx(\"chakra-menu__divider\", className)}\n      {...rest}\n      __css={styles.divider}\n    />\n  )\n}\n\nMenuDivider.displayName = \"MenuDivider\"\n", "import { HTMLAttributes, ReactElement } from \"react\"\nimport { forwardRef, PropsOf, SystemProps } from \"@chakra-ui/system\"\n\nimport { MenuItemProps } from \"./menu-item\"\nimport { useMenuOption, UseMenuOptionOptions } from \"./use-menu\"\nimport { StyledMenuItem } from \"./styled-menu-item\"\nimport { MenuIcon } from \"./menu-icon\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nconst CheckIcon: React.FC<PropsOf<\"svg\">> = (props) => (\n  <svg viewBox=\"0 0 14 14\" width=\"1em\" height=\"1em\" {...props}>\n    <polygon\n      fill=\"currentColor\"\n      points=\"5.5 11.9993304 14 3.49933039 12.5 2 5.5 8.99933039 1.5 4.9968652 0 6.49933039\"\n    />\n  </svg>\n)\n\nexport interface MenuItemOptionProps\n  extends UseMenuOptionOptions,\n    Omit<MenuItemProps, keyof UseMenuOptionOptions | \"icon\"> {\n  /**\n   * @type React.ReactElement\n   */\n  icon?: ReactElement | null\n  /**\n   * @type SystemProps[\"mr\"]\n   */\n  iconSpacing?: SystemProps[\"mr\"]\n}\n\nexport const MenuItemOption = forwardRef<MenuItemOptionProps, \"button\">(\n  (props, ref) => {\n    // menu option item should always be `type=button`, so we omit `type`\n    const { icon, iconSpacing = \"0.75rem\", ...rest } = props\n\n    const optionProps = useMenuOption(rest, ref) as HTMLAttributes<HTMLElement>\n\n    return (\n      <StyledMenuItem\n        {...optionProps}\n        className={cx(\"chakra-menu__menuitem-option\", rest.className)}\n      >\n        {icon !== null && (\n          <MenuIcon\n            fontSize=\"0.8em\"\n            marginEnd={iconSpacing}\n            opacity={props.isChecked ? 1 : 0}\n          >\n            {icon || <CheckIcon />}\n          </MenuIcon>\n        )}\n        <span style={{ flex: 1 }}>{optionProps.children}</span>\n      </StyledMenuItem>\n    )\n  },\n)\n\nMenuItemOption.id = \"MenuItemOption\"\n\nMenuItemOption.displayName = \"MenuItemOption\"\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAA4C;AAM5C,SAAS,qBAAqB,OAA4B;AACxD,QAAM,EAAE,IAAI,IAAI;AAChB,SAAO,IAAI,WAAW,KAAM,IAAI,SAAS,KAAK,eAAe,KAAK,GAAG;AACvE;AAWO,SAAS,YAAY,QAA0B,CAAC,GAAG;AACxD,QAAM,EAAE,UAAU,KAAK,iBAAiB,MAAM,KAAK,IAAI;AAEvD,QAAM,CAAC,MAAM,OAAO,QAAI,uBAAmB,CAAC,CAAC;AAC7C,QAAM,iBAAa,qBAAY;AAE/B,QAAM,QAAQ,MAAM;AAClB,QAAI,WAAW,SAAS;AACtB,mBAAa,WAAW,OAAO;AAC/B,iBAAW,UAAU;IACvB;EACF;AAEA,QAAM,sBAAsB,MAAM;AAChC,UAAM;AACN,eAAW,UAAU,WAAW,MAAM;AACpC,cAAQ,CAAC,CAAC;AACV,iBAAW,UAAU;IACvB,GAAG,OAAO;EACZ;AAEA,8BAAU,MAAM,OAAO,CAAC,CAAC;AAIzB,WAAS,UAAU,IAAc;AAC/B,WAAO,CAAC,UAA+B;AACrC,UAAI,MAAM,QAAQ,aAAa;AAC7B,cAAM,WAAW,CAAC,GAAG,IAAI;AACzB,iBAAS,IAAI;AACb,gBAAQ,QAAQ;AAChB;MACF;AAEA,UAAI,qBAAqB,KAAK,GAAG;AAC/B,cAAM,WAAW,KAAK,OAAO,MAAM,GAAG;AAEtC,YAAI,eAAe,KAAK,GAAG;AACzB,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;QACxB;AAEA,gBAAQ,QAAQ;AAChB,WAAG,SAAS,KAAK,EAAE,CAAC;AAEpB,4BAAoB;MACtB;IACF;EACF;AAEA,SAAO;AACT;;;AC/DO,SAAS,sBACd,OACA,cACA,cACA,aACe;AACf,MAAI,gBAAgB,MAAM;AACxB,WAAO;EACT;AAGA,MAAI,CAAC,aAAa;AAChB,UAAM,YAAY,MAAM;MAAK,CAAC,SAC5B,aAAa,IAAI,EAAE,YAAY,EAAE,WAAW,aAAa,YAAY,CAAC;IACxE;AACA,WAAO;EACT;AAGA,QAAM,gBAAgB,MAAM;IAAO,CAAC,SAClC,aAAa,IAAI,EAAE,YAAY,EAAE,WAAW,aAAa,YAAY,CAAC;EACxE;AAGA,MAAI,cAAc,SAAS,GAAG;AAC5B,QAAI;AAGJ,QAAI,cAAc,SAAS,WAAW,GAAG;AACvC,YAAM,eAAe,cAAc,QAAQ,WAAW;AACtD,kBAAY,eAAe;AAC3B,UAAI,cAAc,cAAc,QAAQ;AACtC,oBAAY;MACd;AACA,aAAO,cAAc,SAAS;IAChC;AAEA,gBAAY,MAAM,QAAQ,cAAc,CAAC,CAAC;AAC1C,WAAO,MAAM,SAAS;EACxB;AAGA,SAAO;AACT;;;ACnDA,IAAAA,gBAA+C;AA6BxC,SAAS,oBAAoC;AAClD,QAAM,gBAAY,sBAAO,oBAAI,IAAI,CAAC;AAClC,QAAM,mBAAmB,UAAU;AAEnC,QAAM,UAAM,2BAAY,CAAC,IAAS,MAAW,UAAe,YAAiB;AAC3E,cAAU,QAAQ,IAAI,UAAU,EAAE,MAAM,IAAI,QAAQ,CAAC;AACrD,OAAG,iBAAiB,MAAM,UAAU,OAAO;EAC7C,GAAG,CAAC,CAAC;AAEL,QAAM,aAAS;IACb,CAAC,IAAS,MAAW,UAAe,YAAiB;AACnD,SAAG,oBAAoB,MAAM,UAAU,OAAO;AAC9C,gBAAU,QAAQ,OAAO,QAAQ;IACnC;IACA,CAAC;EACH;AAEA;IACE,MAAM,MAAM;AACV,uBAAiB,QAAQ,CAAC,OAAO,QAAQ;AACvC,eAAO,MAAM,IAAI,MAAM,MAAM,KAAK,MAAM,OAAO;MACjD,CAAC;IACH;IACA,CAAC,QAAQ,gBAAgB;EAC3B;AAEA,SAAO,EAAE,KAAK,OAAO;AACvB;;;ACxDA,IAAAC,gBAAwB;AAIjB,SAAS,UACd,KACA,OACA;AACA,MAAI,OAAO;AAAM;AAEjB,MAAI,OAAO,QAAQ,YAAY;AAC7B,QAAI,KAAK;AACT;EACF;AAEA,MAAI;AACF,QAAI,UAAU;EAChB,SAAS,OAAP;AACA,UAAM,IAAI,MAAM,wBAAwB,KAAA,aAAkB,GAAA,GAAM;EAClE;AACF;AAEO,SAAS,aAAgB,MAA0C;AACxE,SAAO,CAAC,SAAmB;AACzB,SAAK,QAAQ,CAAC,QAAQ;AACpB,gBAAU,KAAK,IAAI;IACrB,CAAC;EACH;AACF;;;ACzBA,IAAAC,gBAAsC;AAmCtC,SAAS,eAAe,OAA+B;AACrD,QAAM,UAAU,MAAM;AACtB,QAAM,EAAE,SAAS,mBAAAC,mBAAkB,IAAI;AACvC,SACE,YAAY,WAAW,YAAY,cAAcA,uBAAsB;AAE3E;AAQO,SAAS,aAAa,QAA2B,CAAC,GAAG;AAC1D,QAAM;IACJ,KAAK;IACL,YAAAC;IACA,aAAAC;IACA,eAAe;IACf,eAAe;IACf;IACA;IACA;IACA;IACA;IACA,UAAU;IACV;IACA;IACA,GAAG;EACL,IAAI;AAIJ,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,IAAI;AAM7C,QAAM,CAAC,WAAW,YAAY,QAAI,wBAAS,KAAK;AAEhD,QAAM,YAAY,kBAAkB;AAKpC,QAAM,cAAc,CAAC,SAAc;AACjC,QAAI,CAAC;AAAM;AACX,QAAI,KAAK,YAAY,UAAU;AAC7B,kBAAY,KAAK;IACnB;EACF;AAEA,QAAM,WAAW,WAAW,eAAe,gBAAgB;AAC3D,QAAM,gBAAgBD,eAAc,CAACC;AAErC,QAAM,kBAAc;IAClB,CAAC,UAAyC;AACxC,UAAID,aAAY;AACd,cAAM,gBAAgB;AACtB,cAAM,eAAe;AACrB;MACF;AAEA,YAAM,OAAO,MAAM;AACnB,WAAK,MAAM;AACX,iBAAA,OAAA,SAAA,QAAU,KAAA;IACZ;IACA,CAACA,aAAY,OAAO;EACtB;AAEA,QAAM,sBAAkB;IACtB,CAAC,MAAqB;AACpB,UAAI,aAAa,eAAe,CAAC,GAAG;AAClC,UAAE,eAAe;AACjB,UAAE,gBAAgB;AAElB,qBAAa,KAAK;AAElB,kBAAU,OAAO,UAAU,SAAS,iBAAiB,KAAK;MAC5D;IACF;IACA,CAAC,WAAW,SAAS;EACvB;AAEA,QAAM,oBAAgB;IACpB,CAAC,UAA4C;AAC3C,mBAAA,OAAA,SAAA,UAAY,KAAA;AAEZ,UAAIA,eAAc,MAAM,oBAAoB,MAAM,SAAS;AACzD;MACF;AAEA,UAAI,CAAC,eAAe,MAAM,WAAW,KAAK;AAAU;AAEpD,YAAM,qBAAqB,gBAAgB,MAAM,QAAQ;AACzD,YAAM,qBAAqB,gBAAgB,MAAM,QAAQ;AAEzD,UAAI,oBAAoB;AACtB,cAAM,eAAe;AACrB,qBAAa,IAAI;MACnB;AAEA,UAAI,oBAAoB;AACtB,cAAM,eAAe;AACrB,cAAM,OAAO,MAAM;AACnB,aAAK,MAAM;MACb;AAEA,gBAAU,IAAI,UAAU,SAAS,iBAAiB,KAAK;IACzD;IACA;MACEA;MACA;MACA;MACA;MACA;MACA;MACA;IACF;EACF;AAEA,QAAM,kBAAc;IAClB,CAAC,UAA4C;AAC3C,iBAAA,OAAA,SAAA,QAAU,KAAA;AAEV,UAAIA,eAAc,MAAM,oBAAoB,MAAM;AAAS;AAE3D,UAAI,CAAC,eAAe,MAAM,WAAW,KAAK;AAAU;AAEpD,YAAM,qBAAqB,gBAAgB,MAAM,QAAQ;AAEzD,UAAI,oBAAoB;AACtB,cAAM,eAAe;AACrB,qBAAa,KAAK;AAElB,cAAM,OAAO,MAAM;AACnB,aAAK,MAAM;MACb;IACF;IACA,CAAC,cAAc,UAAUA,aAAY,OAAO;EAC9C;AAEA,QAAM,wBAAoB;IACxB,CAAC,UAAsB;AACrB,UAAI,MAAM,WAAW;AAAG;AACxB,mBAAa,KAAK;AAClB,gBAAU,OAAO,UAAU,WAAW,mBAAmB,KAAK;IAChE;IACA,CAAC,SAAS;EACZ;AAEA,QAAM,sBAAkB;IACtB,CAAC,UAAyC;AACxC,UAAI,MAAM,WAAW;AAAG;AAExB,UAAIA,aAAY;AACd,cAAM,gBAAgB;AACtB,cAAM,eAAe;AACrB;MACF;AAEA,UAAI,CAAC,UAAU;AACb,qBAAa,IAAI;MACnB;AAEA,YAAM,SAAS,MAAM;AACrB,aAAO,MAAM,EAAE,eAAe,KAAK,CAAC;AAEpC,gBAAU,IAAI,UAAU,WAAW,mBAAmB,KAAK;AAE3D,qBAAA,OAAA,SAAA,YAAc,KAAA;IAChB;IACA,CAACA,aAAY,UAAU,aAAa,WAAW,iBAAiB;EAClE;AAEA,QAAM,oBAAgB;IACpB,CAAC,UAAyC;AACxC,UAAI,MAAM,WAAW;AAAG;AAExB,UAAI,CAAC,UAAU;AACb,qBAAa,KAAK;MACpB;AAEA,mBAAA,OAAA,SAAA,UAAY,KAAA;IACd;IACA,CAAC,WAAW,QAAQ;EACtB;AAEA,QAAM,sBAAkB;IACtB,CAAC,UAAyC;AACxC,UAAIA,aAAY;AACd,cAAM,eAAe;AACrB;MACF;AAEA,qBAAA,OAAA,SAAA,YAAc,KAAA;IAChB;IACA,CAACA,aAAY,WAAW;EAC1B;AAEA,QAAM,uBAAmB;IACvB,CAAC,UAAyC;AACxC,UAAI,WAAW;AACb,cAAM,eAAe;AACrB,qBAAa,KAAK;MACpB;AACA,sBAAA,OAAA,SAAA,aAAe,KAAA;IACjB;IACA,CAAC,WAAW,YAAY;EAC1B;AAEA,QAAM,MAAM,UAAU,SAAS,WAAW;AAE1C,MAAI,UAAU;AACZ,WAAO;MACL,GAAG;MACH;MACA,MAAM;MACN,iBAAiB,gBAAgB,SAAYA;MAC7C,UAAU;MACV,SAAS;MACT;MACA;MACA;MACA;MACA;MACA;IACF;EACF;AAEA,SAAO;IACL,GAAG;IACH;IACA,MAAM;IACN,eAAe,SAAS,SAAS;IACjC,iBAAiBA,cAAc,SAAmB;IAClD,UAAU,gBAAgB,SAAY;IACtC,SAAS;IACT,aAAa;IACb,WAAW;IACX,SAAS;IACT,WAAW;IACX,aAAa;IACb,cAAc;EAChB;AACF;;;AC7RA,IAAAE,gBAA2C;;;;;;;AAMpC,SAAS,UAAU,OAAe;AACvC,SAAO,MAAM,KAAK,CAAC,GAAG,MAAM;AAC1B,UAAM,UAAU,EAAE,wBAAwB,CAAC;AAE3C,QACE,UAAU,KAAK,+BACf,UAAU,KAAK,gCACf;AAEA,aAAO;IACT;AAEA,QACE,UAAU,KAAK,+BACf,UAAU,KAAK,4BACf;AAEA,aAAO;IACT;AAEA,QACE,UAAU,KAAK,kCACf,UAAU,KAAK,2CACf;AACA,YAAM,MAAM,8BAA8B;IAC5C,OAAO;AACL,aAAO;IACT;EACF,CAAC;AACH;AAEO,IAAM,YAAY,CAAC,OACxB,OAAO,MAAM,YAAY,cAAc,MAAM,GAAG,aAAa,KAAK;AAE7D,SAAS,aAAa,SAAiB,KAAa,MAAe;AACxE,MAAI,OAAO,UAAU;AACrB,MAAI,QAAQ,QAAQ;AAAK,WAAO;AAChC,SAAO;AACT;AAEO,SAAS,aAAa,SAAiB,KAAa,MAAe;AACxE,MAAI,OAAO,UAAU;AACrB,MAAI,QAAQ,OAAO;AAAG,WAAO;AAC7B,SAAO;AACT;AAEO,IAAM,sBACX,OAAO,WAAW,cAAc,gCAAkB;AAE7C,IAAM,OAAO,CAAI,UAAe;;;ACxBhC,IAAM,qBAAN,MAGL;EAHK,cAAA;AAIL,kBAAA,MAAQ,eAAc,oBAAI,IAAyB,CAAA;AAEnD,kBAAA,MAAA,YAAW,CAAC,kBAAmD;AAC7D,UAAI,iBAAiB;AAAM;AAE3B,UAAI,UAAU,aAAa,GAAG;AAC5B,eAAO,KAAK,aAAa,aAAa;MACxC;AAEA,aAAO,CAAC,SAAmB;AACzB,aAAK,aAAa,MAAM,aAAa;MACvC;IACF,CAAA;AAEA,kBAAA,MAAA,cAAa,CAAC,SAAY;AACxB,WAAK,YAAY,OAAO,IAAI;AAC5B,YAAM,SAAS,UAAU,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,CAAC;AAC5D,WAAK,YAAY,MAAM;IACzB,CAAA;AAEA,kBAAA,MAAA,WAAU,MAAM;AACd,WAAK,YAAY,MAAM;IACzB,CAAA;AAEA,kBAAA,MAAQ,eAAc,CAAC,gBAAwB;AAC7C,WAAK,YAAY,QAAQ,CAAC,eAAe;AACvC,cAAM,QAAQ,YAAY,QAAQ,WAAW,IAAI;AACjD,mBAAW,QAAQ;AACnB,mBAAW,KAAK,QAAQ,OAAO,IAAI,WAAW,MAAM,SAAS;MAC/D,CAAC;IACH,CAAA;AAEA,kBAAA,MAAA,SAAQ,MAAM,KAAK,YAAY,IAAA;AAE/B,kBAAA,MAAA,gBAAe,MAAM,KAAK,cAAc,EAAE,MAAA;AAE1C,kBAAA,MAAA,UAAS,MAAM;AACb,YAAM,SAAS,MAAM,KAAK,KAAK,YAAY,OAAO,CAAC;AACnD,aAAO,OAAO,KAAK,CAAC,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK;IAChD,CAAA;AAEA,kBAAA,MAAA,iBAAgB,MAAM;AACpB,aAAO,KAAK,OAAO,EAAE,OAAO,CAAC,eAAe,CAAC,WAAW,QAAQ;IAClE,CAAA;AAEA,kBAAA,MAAA,QAAO,CAAC,UAAkB;AACxB,UAAI,KAAK,MAAM,MAAM;AAAG,eAAO;AAC/B,aAAO,KAAK,OAAO,EAAE,KAAK;IAC5B,CAAA;AAEA,kBAAA,MAAA,eAAc,CAAC,UAAkB;AAC/B,UAAI,KAAK,aAAa,MAAM;AAAG,eAAO;AACtC,aAAO,KAAK,cAAc,EAAE,KAAK;IACnC,CAAA;AAEA,kBAAA,MAAA,SAAQ,MAAM,KAAK,KAAK,CAAC,CAAA;AAEzB,kBAAA,MAAA,gBAAe,MAAM,KAAK,YAAY,CAAC,CAAA;AAEvC,kBAAA,MAAA,QAAO,MAAM,KAAK,KAAK,KAAK,YAAY,OAAO,CAAC,CAAA;AAEhD,kBAAA,MAAA,eAAc,MAAM;AAClB,YAAM,YAAY,KAAK,cAAc,EAAE,SAAS;AAChD,aAAO,KAAK,YAAY,SAAS;IACnC,CAAA;AAEA,kBAAA,MAAA,WAAU,CAAC,SAAmB;AArGhC,UAAA,IAAA;AAsGI,UAAI,CAAC;AAAM,eAAO;AAClB,cAAO,MAAA,KAAA,KAAK,YAAY,IAAI,IAAI,MAAzB,OAAA,SAAA,GAA4B,UAA5B,OAAA,KAAqC;IAC9C,CAAA;AAEA,kBAAA,MAAA,kBAAiB,CAAC,SAAmB;AACnC,UAAI,QAAQ;AAAM,eAAO;AACzB,aAAO,KAAK,cAAc,EAAE,UAAU,CAAC,MAAM,EAAE,KAAK,WAAW,IAAI,CAAC;IACtE,CAAA;AAEA,kBAAA,MAAA,QAAO,CAAC,OAAe,OAAO,SAAS;AACrC,YAAM,OAAO,aAAa,OAAO,KAAK,MAAM,GAAG,IAAI;AACnD,aAAO,KAAK,KAAK,IAAI;IACvB,CAAA;AAEA,kBAAA,MAAA,eAAc,CAAC,OAAe,OAAO,SAAS;AAC5C,YAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,UAAI,CAAC;AAAM;AACX,YAAM,eAAe,KAAK,eAAe,KAAK,IAAI;AAClD,YAAM,mBAAmB;QACvB;QACA,KAAK,aAAa;QAClB;MACF;AACA,aAAO,KAAK,YAAY,gBAAgB;IAC1C,CAAA;AAEA,kBAAA,MAAA,QAAO,CAAC,OAAe,OAAO,SAAS;AACrC,YAAM,OAAO,aAAa,OAAO,KAAK,MAAM,IAAI,GAAG,IAAI;AACvD,aAAO,KAAK,KAAK,IAAI;IACvB,CAAA;AAEA,kBAAA,MAAA,eAAc,CAAC,OAAe,OAAO,SAAS;AAC5C,YAAM,OAAO,KAAK,KAAK,KAAK;AAC5B,UAAI,CAAC;AAAM;AACX,YAAM,eAAe,KAAK,eAAe,KAAK,IAAI;AAClD,YAAM,mBAAmB;QACvB;QACA,KAAK,aAAa,IAAI;QACtB;MACF;AACA,aAAO,KAAK,YAAY,gBAAgB;IAC1C,CAAA;AAEA,kBAAA,MAAQ,gBAAe,CAAC,MAAgB,YAAmC;AACzE,UAAI,CAAC,QAAQ,KAAK,YAAY,IAAI,IAAI;AAAG;AAEzC,YAAM,OAAO,MAAM,KAAK,KAAK,YAAY,KAAK,CAAC,EAAE,OAAO,IAAI;AAC5D,YAAM,SAAS,UAAU,IAAI;AAE7B,UAAI,WAAA,OAAA,SAAA,QAAS,UAAU;AACrB,gBAAQ,WAAW,CAAC,CAAC,QAAQ;MAC/B;AAEA,YAAM,aAAa,EAAE,MAAM,OAAO,IAAI,GAAG,QAAQ;AAEjD,WAAK,YAAY,IAAI,MAAM,UAA8B;AAEzD,WAAK,YAAY,MAAM;IACzB,CAAA;EAAA;AACF;;;AC/JA,IAAAC,gBAAiC;AAQjC,SAAS,iBAGL;AACF,QAAM,kBAAc,sBAAO,IAAI,mBAAyB,CAAC;AACzD,sBAAoB,MAAM;AACxB,WAAO,MAAM,YAAY,QAAQ,QAAQ;EAC3C,CAAC;AACD,SAAO,YAAY;AACrB;AAaA,IAAM,CAAC,4BAA4B,qBAAqB,IACtD,cAAoC;EAClC,MAAM;EACN,cACE;AACJ,CAAC;AASH,SAAS,cAGP,SAAgC;AAChC,QAAM,cAAc,sBAAsB;AAC1C,QAAM,CAAC,OAAO,QAAQ,QAAI,wBAAS,EAAE;AACrC,QAAM,UAAM,sBAAU,IAAI;AAE1B,sBAAoB,MAAM;AACxB,WAAO,MAAM;AACX,UAAI,CAAC,IAAI;AAAS;AAClB,kBAAY,WAAW,IAAI,OAAO;IACpC;EACF,GAAG,CAAC,CAAC;AAEL,sBAAoB,MAAM;AACxB,QAAI,CAAC,IAAI;AAAS;AAClB,UAAM,YAAY,OAAO,IAAI,QAAQ,QAAQ,OAAO,CAAC;AACrD,QAAI,SAAS,aAAa,CAAC,OAAO,MAAM,SAAS,GAAG;AAClD,eAAS,SAAS;IACpB;EACF,CAAC;AAED,QAAM,cAAc,UAChB,KAA2B,YAAY,SAAS,OAAO,CAAC,IACxD,KAA2B,YAAY,QAAQ;AAEnD,SAAO;IACL;IACA;IACA,cAAc,YAAY,eAAe,IAAI,OAAO;IACpD,UAAU,UAAU,aAAa,GAAG;EACtC;AACF;AAOO,SAAS,0BAGZ;AAEF,QAAM,kBAAkB,KAA0B,0BAA0B;AAE5E,QAAM,yBAAyB,MAC7B,KAA+B,sBAAsB,CAAC;AAExD,QAAM,iBAAiB,CAAC,YACtB,cAAoB,OAAO;AAE7B,QAAM,kBAAkB,MAAM,eAAqB;AAEnD,SAAO;;IAEL;;IAEA;;IAEA;;IAEA;EACF;AACF;;;AC9GA,SAASC,WAAU,IAAI;AACrB,SAAO,MAAM,QAAQ,OAAO,MAAM,YAAY,cAAc,MAAM,GAAG,aAAa,KAAK;AACzF;AACA,SAAS,cAAc,IAAI;AACzB,MAAI;AACJ,MAAI,CAACA,WAAU,EAAE;AACf,WAAO;AACT,QAAM,OAAO,KAAK,GAAG,cAAc,gBAAgB,OAAO,KAAK;AAC/D,SAAO,cAAc,IAAI;AAC3B;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI,IAAI;AACR,UAAQ,MAAM,KAAK,iBAAiB,IAAI,MAAM,OAAO,SAAS,GAAG,gBAAgB,OAAO,KAAK;AAC/F;AACA,SAAS,iBAAiB,MAAM;AAC9B,SAAOA,WAAU,IAAI,IAAI,KAAK,gBAAgB;AAChD;AAQA,SAAS,iBAAiB,MAAM;AAC9B,SAAO,iBAAiB,IAAI,EAAE;AAChC;;;ACpBA,IAAI,cAAc,CAAC,YAAY,QAAQ,aAAa,UAAU;AAC9D,IAAI,sBAAsB,CAAC,YAAY,YAAY,OAAO,KAAK,QAAQ,aAAa;AACpF,SAAS,WAAW,SAAS;AAC3B,SAAO,QAAQ,QAAQ,aAAa,UAAU,CAAC,MAAM,QAAQ,QAAQ,QAAQ,aAAa,eAAe,CAAC,MAAM;AAClH;AAaA,SAAS,SAAS,SAAS;AACzB,MAAI,QAAQ,iBAAiB,SAAS,QAAQ,aAAa;AACzD,WAAO;AACT,SAAO,QAAQ;AACjB;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM,QAAQ,QAAQ,aAAa,iBAAiB;AACpD,SAAO,UAAU,WAAW,SAAS;AACvC;AACA,SAAS,YAAY,SAAS;AAC5B,MAAI,CAAC,cAAc,OAAO,KAAK,SAAS,OAAO,KAAK,WAAW,OAAO,GAAG;AACvE,WAAO;AAAA,EACT;AACA,QAAM,EAAE,UAAU,IAAI;AACtB,QAAM,gBAAgB,CAAC,SAAS,UAAU,YAAY,QAAQ;AAC9D,MAAI,cAAc,QAAQ,SAAS,KAAK;AACtC,WAAO;AACT,QAAM,SAAS;AAAA,IACb,GAAG,MAAM,QAAQ,aAAa,MAAM;AAAA,IACpC,OAAO,MAAM,QAAQ,aAAa,UAAU;AAAA,IAC5C,OAAO,MAAM,QAAQ,aAAa,UAAU;AAAA,EAC9C;AACA,MAAI,aAAa,QAAQ;AACvB,WAAO,OAAO,SAAS,EAAE;AAAA,EAC3B;AACA,MAAI,kBAAkB,OAAO;AAC3B,WAAO;AACT,SAAO,YAAY,OAAO;AAC5B;AACA,SAAS,WAAW,SAAS;AAC3B,MAAI,CAAC;AACH,WAAO;AACT,SAAO,cAAc,OAAO,KAAK,YAAY,OAAO,KAAK,CAAC,oBAAoB,OAAO;AACvF;;;AC7BA,IAAI,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,IAAI,sBAAsB,gBAAgB,KAAK;;;AC5C/C,IAAAC,gBAA0B;AAwBnB,SAAS,iBACd,QACA,OACA,SACA,SACA;AACA,QAAM,WAAW,eAAe,OAAO;AAEvC,+BAAU,MAAM;AACd,UAAM,OAAO,OAAO,WAAW,aAAa,OAAO,IAAI,UAAA,OAAA,SAAU;AAEjE,QAAI,CAAC,WAAW,CAAC;AAAM;AAEvB,SAAK,iBAAiB,OAAO,UAAU,OAAO;AAC9C,WAAO,MAAM;AACX,WAAK,oBAAoB,OAAO,UAAU,OAAO;IACnD;EACF,GAAG,CAAC,OAAO,QAAQ,SAAS,UAAU,OAAO,CAAC;AAE9C,SAAO,MAAM;AACX,UAAM,OAAO,OAAO,WAAW,aAAa,OAAO,IAAI,UAAA,OAAA,SAAU;AACjE,YAAA,OAAA,SAAA,KAAM,oBAAoB,OAAO,UAAU,OAAA;EAC7C;AACF;;;ACrCA,IAAAC,gBAAoC;AAQpC,SAAS,mBAAmB,cAA4C;AACtE,QAAM,KAAK,aAAa;AACxB,MAAI,CAAC;AAAI,WAAO;AAEhB,QAAM,gBAAgB,iBAAiB,EAAE;AAEzC,MAAI,CAAC;AAAe,WAAO;AAC3B,MAAI,GAAG,SAAS,aAAa;AAAG,WAAO;AACvC,MAAI,WAAW,aAAa;AAAG,WAAO;AAEtC,SAAO;AACT;AASO,SAAS,eACd,cACA,SACA;AACA,QAAM,EAAE,aAAa,iBAAiB,SAAS,SAAS,IAAI;AAE5D,QAAM,cAAc,mBAAmB,CAAC;AAExC,kBAAgB,MAAM;AACpB,QAAI,CAAC;AAAa;AAElB,QAAI,mBAAmB,YAAY,GAAG;AACpC;IACF;AAEA,UAAM,MAAK,YAAA,OAAA,SAAA,SAAU,YAAW,aAAa;AAE7C,QAAI;AAEJ,QAAI,IAAI;AACN,cAAQ,sBAAsB,MAAM;AAClC,WAAG,MAAM,EAAE,eAAe,KAAK,CAAC;MAClC,CAAC;AACD,aAAO,MAAM;AACX,6BAAqB,KAAK;MAC5B;IACF;EACF,GAAG,CAAC,aAAa,cAAc,QAAQ,CAAC;AAC1C;;;AChEA,IAAM,QAAQ,CAAC,OAAe,cAAuB;EACnD,KAAK;EACL,QAAQ,WAAW,OAAO,KAAA,KAAU,QAAA,MAAc,OAAO,KAAA;AAC3D;AAEO,IAAM,UAAU;EACrB,kBAAkB,MAAM,6BAA6B;EACrD,WAAW,MAAM,uBAAuB,KAAK;EAC7C,eAAe,MAAM,0BAA0B;EAC/C,SAAS,MAAM,mBAAmB;EAClC,iBAAiB,MAAM,2BAA2B;EAClD,aAAa,MAAM,uBAAuB;AAC5C;AAEO,SAAS,aAAa,WAAsB;AACjD,MAAI,UAAU,SAAS,KAAK;AAC1B,WAAO;AACT,MAAI,UAAU,SAAS,QAAQ;AAC7B,WAAO;AACT,MAAI,UAAU,SAAS,OAAO;AAC5B,WAAO;AACT,MAAI,UAAU,SAAS,MAAM;AAC3B,WAAO;AACX;AAEA,IAAM,aAAqC;EACzC,KAAK;EACL,aAAa;EACb,WAAW;EAEX,QAAQ;EACR,gBAAgB;EAChB,cAAc;EAEd,MAAM;EACN,cAAc;EACd,YAAY;EAEZ,OAAO;EACP,eAAe;EACf,aAAa;AACf;AAEO,IAAM,oBAAoB,CAAC,cAAyB,WAAW,SAAS;AAE/E,IAAM,wBAAwB;EAC5B,QAAQ;EACR,QAAQ;AACV;AAEO,SAAS,wBACd,OACA;AACA,MAAI;AAIJ,MAAI,OAAO,UAAU,UAAU;AAC7B,qBAAiB;MACf,SAAS;MACT,SAAS,EAAE,GAAG,uBAAuB,GAAG,MAAM;IAChD;EACF,OAAO;AACL,qBAAiB;MACf,SAAS;MACT,SAAS;IACX;EACF;AACA,SAAO;AACT;;;AC/DO,IAAM,aAA0C;EACrD,MAAM;EACN,SAAS;EACT,OAAO;EACP,UAAU,CAAC,eAAe;EAC1B,IAAI,CAAC,EAAE,MAAM,MAAM;AACjB,UAAM,OAAO,OAAO,QAAQ,GAAG,MAAM,MAAM,UAAU,KAAA;EACvD;EACA,QACE,CAAC,EAAE,MAAM,MACT,MAAM;AACJ,UAAM,YAAY,MAAM,SAAS;AACjC,UAAM,SAAS,OAAO,MAAM,QAAQ,GAAG,UAAU,WAAA;EACnD;AACJ;AASO,IAAM,kBAAoD;EAC/D,MAAM;EACN,SAAS;EACT,OAAO;EACP,IAAI,CAAC,EAAE,MAAM,MAAM;AACjB,uBAAmB,KAAK;EAC1B;EACA,QACE,CAAC,EAAE,MAAM,MACT,MAAM;AACJ,uBAAmB,KAAK;EAC1B;AACJ;AAEA,IAAM,qBAAqB,CAAC,UAAiB;AAC3C,QAAM,SAAS,OAAO,MAAM;IAC1B,QAAQ,gBAAgB;IACxB,kBAAkB,MAAM,SAAS;EACnC;AACF;AAOO,IAAM,gBAAgD;EAC3D,MAAM;EACN,SAAS;EACT,OAAO;EACP,IAAI,CAAC,EAAE,MAAM,MAAM;AACjB,mBAAe,KAAK;EACtB;AACF;AAEA,IAAM,iBAAiB,CAAC,UAA0B;AAlElD,MAAA;AAmEE,MAAI,CAAC,MAAM;AAAW;AACtB,QAAM,YAAY,cAAc,MAAM,SAAS;AAE/C,QAAI,KAAA,MAAM,aAAN,OAAA,SAAA,GAAgB,UAAS,WAAW;AACtC,WAAO,OAAO,MAAM,SAAS,MAAM,OAAO;MACxC,CAAC,UAAU,QAAQ,GAAG,UAAU;MAChC,OAAO,QAAQ,UAAU;MACzB,QAAQ,QAAQ,UAAU;MAC1B,QAAQ;IACV,CAAC;AAED,UAAM,OAAO;MACX,CAAC,QAAQ,cACN,GAAG,GAAG,QAAQ,QAAQ,UAAU,MAAA;MACnC,CAAC,QAAQ,YAAY,GAAG,GAAG,QAAQ,QAAQ,cAAc,MAAA;IAC3D;AAEA,eAAW,YAAY,MAAM;AAC3B,YAAM,SAAS,MAAM,MAAM,YAAY,UAAU,KAAK,QAAQ,CAAC;IACjE;EACF;AACF;AAEA,IAAM,gBAAgB,CAAC,cAAyB;AAC9C,MAAI,UAAU,WAAW,KAAK,GAAG;AAC/B,WAAO,EAAE,UAAU,UAAU,OAAO,QAAQ,YAAY,OAAO;EACjE;AACA,MAAI,UAAU,WAAW,QAAQ,GAAG;AAClC,WAAO,EAAE,UAAU,OAAO,OAAO,QAAQ,YAAY,OAAO;EAC9D;AACA,MAAI,UAAU,WAAW,MAAM,GAAG;AAChC,WAAO,EAAE,UAAU,SAAS,OAAO,QAAQ,YAAY,OAAO;EAChE;AACA,MAAI,UAAU,WAAW,OAAO,GAAG;AACjC,WAAO,EAAE,UAAU,QAAQ,OAAO,QAAQ,YAAY,OAAO;EAC/D;AACF;AAOO,IAAM,aAA0C;EACrD,MAAM;EACN,SAAS;EACT,OAAO;EACP,UAAU,CAAC,OAAO;EAClB,IAAI,CAAC,EAAE,MAAM,MAAM;AACjB,wBAAoB,KAAK;EAC3B;EACA,QACE,CAAC,EAAE,MAAM,MACT,MAAM;AACJ,wBAAoB,KAAK;EAC3B;AACJ;AAEA,IAAM,sBAAsB,CAAC,UAAiB;AAC5C,MAAI,CAAC,MAAM,SAAS;AAAO;AAE3B,QAAM,QAAQ,MAAM,SAAS,MAAM;IACjC;EACF;AAEA,MAAI,CAAC;AAAO;AACZ,QAAM,YAAY,aAAa,MAAM,SAAS;AAC9C,MAAI,WAAW;AACb,UAAM,MAAM,YAAY,iCAAiC,SAAS;EACpE;AAEA,SAAO,OAAO,MAAM,OAAO;IACzB,WAAW;IACX,YAAY,QAAQ,QAAQ;IAC5B,KAAK;IACL,MAAM;IACN,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,WAAW;EACb,CAAC;AACH;;;ACvIA,IAAM,WAAgE;EACpE,eAAe,EAAE,KAAK,cAAc,KAAK,cAAc;EACvD,aAAa,EAAE,KAAK,YAAY,KAAK,YAAY;EACjD,aAAa,EAAE,KAAK,eAAe,KAAK,aAAa;EACrD,WAAW,EAAE,KAAK,aAAa,KAAK,WAAW;EAC/C,OAAO,EAAE,KAAK,QAAQ,KAAK,QAAQ;EACnC,KAAK,EAAE,KAAK,SAAS,KAAK,OAAO;AACnC;AAEA,IAAM,YAAmD;EACvD,cAAc;EACd,YAAY;EACZ,aAAa;EACb,WAAW;EACX,gBAAgB;EAChB,cAAc;AAChB;AAEO,SAAS,mBACd,WACA,MAAqB,OACV;AAnCb,MAAA,IAAA;AAoCE,QAAM,UAAU,KAAA,SAAiB,SAAS,MAA1B,OAAA,SAAA,GAA8B,GAAA,MAAQ;AACtD,MAAI,QAAQ;AAAO,WAAO;AAC1B,UAAQ,KAAA,UAAkB,SAAS,MAA3B,OAAA,KAAgC;AAC1C;;;AC/BA,IAAAC,gBAA+C;AAqGxC,SAAS,UAAU,QAAwB,CAAC,GAAG;AACpD,QAAM;IACJ,UAAU;IACV;IACA,WAAW,gBAAgB;IAC3B,WAAW;IACX,eAAe;IACf,iBAAiB;IACjB;IACA,SAAS;IACT,OAAO;IACP,WAAW;IACX,kBAAkB;IAClB,YAAAC;IACA,YAAY;EACd,IAAI;AAEJ,QAAM,gBAAY,sBAAwC,IAAI;AAC9D,QAAM,aAAS,sBAA2B,IAAI;AAC9C,QAAM,eAAW,sBAAwB,IAAI;AAC7C,QAAM,YAAY,mBAAmB,eAAe,SAAS;AAE7D,QAAM,cAAU,sBAAO,MAAM;EAAC,CAAC;AAE/B,QAAM,kBAAc,2BAAY,MAAM;AArIxC,QAAA;AAsII,QAAI,CAAC,WAAW,CAAC,UAAU,WAAW,CAAC,OAAO;AAAS;AAGvD,KAAA,KAAA,QAAQ,YAAR,OAAA,SAAA,GAAA,KAAA,OAAA;AAEA,aAAS,UAAU,aAAa,UAAU,SAAS,OAAO,SAAS;MACjE;MACA,WAAW;QACO;QACA;QACA;QAChB;UACE,GAAmB;UACnB,SAAS,CAAC,CAACA;QACb;QACA;UACE,MAAM;UACN,GAAG,wBAAwB,cAAc;QAC3C;QACA;UACE,MAAM;UACN,SAAS,EAAE,SAAS,aAAa;QACnC;QACA;UACE,MAAM;UACN,SAAS;YACP,QAAQ,UAAA,OAAA,SAAU,CAAC,GAAG,MAAM;UAC9B;QACF;QACA;UACE,MAAM;UACN,SAAS,CAAC,CAAC;UACX,SAAS,EAAE,SAAS,EAAE;QACxB;QACA;UACE,MAAM;UACN,SAAS,CAAC,CAAC;UACX,SAAS,EAAE,SAAS;QACtB;;QAEA,GAAI,aAAA,OAAA,YAAa,CAAC;MACpB;MACA;IACF,CAAC;AAGD,aAAS,QAAQ,YAAY;AAE7B,YAAQ,UAAU,SAAS,QAAQ;EACrC,GAAG;IACD;IACA;IACA;IACAA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,CAAC;AAED,+BAAU,MAAM;AACd,WAAO,MAAM;AAvMjB,UAAA;AA4MM,UAAI,CAAC,UAAU,WAAW,CAAC,OAAO,SAAS;AACzC,SAAA,KAAA,SAAS,YAAT,OAAA,SAAA,GAAkB,QAAA;AAClB,iBAAS,UAAU;MACrB;IACF;EACF,GAAG,CAAC,CAAC;AAEL,QAAM,mBAAe;IACnB,CAAqC,SAAmB;AACtD,gBAAU,UAAU;AACpB,kBAAY;IACd;IACA,CAAC,WAAW;EACd;AAEA,QAAM,wBAAgC;IACpC,CAACC,SAAQ,CAAC,GAAG,MAAM,UAAU;MAC3B,GAAGA;MACH,KAAK,UAAU,cAAc,GAAG;IAClC;IACA,CAAC,YAAY;EACf;AAEA,QAAM,gBAAY;IAChB,CAAwB,SAAmB;AACzC,aAAO,UAAU;AACjB,kBAAY;IACd;IACA,CAAC,WAAW;EACd;AAEA,QAAM,qBAA6B;IACjC,CAACA,SAAQ,CAAC,GAAG,MAAM,UAAU;MAC3B,GAAGA;MACH,KAAK,UAAU,WAAW,GAAG;MAC7B,OAAO;QACL,GAAGA,OAAM;QACT,UAAU;QACV,UAAUD,cAAa,SAAY;QACnC,OAAO;MACT;IACF;IACA,CAAC,UAAU,WAAWA,WAAU;EAClC;AAEA,QAAM,oBAA4B,2BAAY,CAACC,SAAQ,CAAC,GAAG,MAAM,SAAS;AACxE,UAAM,EAAE,MAAM,aAAa,IAAI,OAAO,GAAG,KAAK,IAAIA;AAClD,WAAO;MACL,GAAG;MACH;MACA,qBAAqB;MACrB,OAAOC,eAAcD,MAAK;IAC5B;EACF,GAAG,CAAC,CAAC;AAEL,QAAM,yBAAiC;IACrC,CAACA,SAAQ,CAAC,GAAG,MAAM,UAAU;MAC3B,GAAGA;MACH;MACA,2BAA2B;IAC7B;IACA,CAAC;EACH;AAEA,SAAO;IACL,SAAS;AA7Qb,UAAA;AA8QM,OAAA,KAAA,SAAS,YAAT,OAAA,SAAA,GAAkB,OAAA;IACpB;IACA,cAAc;AAhRlB,UAAA;AAiRM,OAAA,KAAA,SAAS,YAAT,OAAA,SAAA,GAAkB,YAAA;IACpB;IACA,iBAAiB,QAAQ,gBAAgB;IACzC;IACA;IACA;IACA;IACA;IACA;EACF;AACF;AAEA,SAASC,eAAc,OAAY;AACjC,QAAM,EAAE,MAAM,aAAa,IAAI,MAAM,IAAI;AACzC,QAAM,gBAAgB,EAAE,GAAG,OAAO,UAAU,WAAW;AACvD,MAAI,MAAM;AACR,kBAAc,qBAAqB,IAAI;EACzC;AACA,MAAI,aAAa;AACf,kBAAc,6BAA6B,IAAI;EACjD;AACA,MAAI,IAAI;AACN,kBAAc,mBAAmB,IAAI;EACvC;AACA,SAAO;AACT;;;ACzSA,IAAAC,iBAAoD;AAkB7C,SAAS,cAAc,QAA4B,CAAC,GAAG;AAC5D,QAAM;IACJ,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,IAAI;EACN,IAAI;AAEJ,QAAM,aAAa,eAAe,UAAU;AAC5C,QAAM,cAAc,eAAe,WAAW;AAE9C,QAAM,CAAC,aAAa,SAAS,QAAI,yBAAS,MAAM,iBAAiB,KAAK;AAEtE,QAAM,SAAS,eAAe,SAAY,aAAa;AAEvD,QAAM,eAAe,eAAe;AAEpC,QAAM,UAAM,sBAAM;AAClB,QAAM,KAAK,UAAA,OAAA,SAAU,cAAc,GAAA;AAEnC,QAAM,cAAU,4BAAY,MAAM;AAChC,QAAI,CAAC,cAAc;AACjB,gBAAU,KAAK;IACjB;AACA,mBAAA,OAAA,SAAA,YAAA;EACF,GAAG,CAAC,cAAc,WAAW,CAAC;AAE9B,QAAM,aAAS,4BAAY,MAAM;AAC/B,QAAI,CAAC,cAAc;AACjB,gBAAU,IAAI;IAChB;AACA,kBAAA,OAAA,SAAA,WAAA;EACF,GAAG,CAAC,cAAc,UAAU,CAAC;AAE7B,QAAM,eAAW,4BAAY,MAAM;AACjC,QAAI,QAAQ;AACV,cAAQ;IACV,OAAO;AACL,aAAO;IACT;EACF,GAAG,CAAC,QAAQ,QAAQ,OAAO,CAAC;AAE5B,WAAS,eAAeC,SAAmB,CAAC,GAAc;AACxD,WAAO;MACL,GAAGA;MACH,iBAAiB;MACjB,iBAAiB;MACjB,QAAQ,OAAO;AAlErB,YAAA;AAmEQ,SAAA,KAAAA,OAAM,YAAN,OAAA,SAAA,GAAA,KAAAA,QAAgB,KAAA;AAChB,iBAAS;MACX;IACF;EACF;AAEA,WAAS,mBAAmBA,SAAmB,CAAC,GAAc;AAC5D,WAAO;MACL,GAAGA;MACH,QAAQ,CAAC;MACT;IACF;EACF;AAEA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;;;AC1FA,IAAAC,iBAAkC;AAsB3B,SAAS,gBAAgB,OAA6B;AAC3D,QAAM,EAAE,KAAK,SAAS,UAAU,KAAK,IAAI;AACzC,QAAM,eAAe,eAAe,OAAO;AAE3C,QAAM,eAAW,uBAAO;IACtB,eAAe;IACf,2BAA2B;EAC7B,CAAC;AAED,QAAM,QAAQ,SAAS;AAEvB,gCAAU,MAAM;AACd,QAAI,CAAC;AAAS;AACd,UAAM,gBAAqB,CAAC,MAAoB;AAC9C,UAAI,aAAa,GAAG,GAAG,GAAG;AACxB,cAAM,gBAAgB;MACxB;IACF;AAEA,UAAM,YAAiB,CAAC,UAAsB;AAC5C,UAAI,MAAM,2BAA2B;AACnC,cAAM,4BAA4B;AAClC;MACF;AAEA,UAAI,MAAM,iBAAiB,WAAW,aAAa,OAAO,GAAG,GAAG;AAC9D,cAAM,gBAAgB;AACtB,qBAAa,KAAK;MACpB;IACF;AAEA,UAAM,aAAa,CAAC,UAAsB;AACxC,YAAM,4BAA4B;AAClC,UAAI,WAAW,MAAM,iBAAiB,aAAa,OAAO,GAAG,GAAG;AAC9D,cAAM,gBAAgB;AACtB,qBAAa,KAAK;MACpB;IACF;AAEA,UAAM,MAAMC,kBAAiB,IAAI,OAAO;AACxC,QAAI,iBAAiB,aAAa,eAAe,IAAI;AACrD,QAAI,iBAAiB,WAAW,WAAW,IAAI;AAC/C,QAAI,iBAAiB,cAAc,eAAe,IAAI;AACtD,QAAI,iBAAiB,YAAY,YAAY,IAAI;AAEjD,WAAO,MAAM;AACX,UAAI,oBAAoB,aAAa,eAAe,IAAI;AACxD,UAAI,oBAAoB,WAAW,WAAW,IAAI;AAClD,UAAI,oBAAoB,cAAc,eAAe,IAAI;AACzD,UAAI,oBAAoB,YAAY,YAAY,IAAI;IACtD;EACF,GAAG,CAAC,SAAS,KAAK,cAAc,OAAO,OAAO,CAAC;AACjD;AAEA,SAAS,aAAa,OAAc,KAAmC;AA5EvE,MAAA;AA6EE,QAAM,SAAS,MAAM;AAErB,MAAI,QAAQ;AACV,UAAM,MAAMA,kBAAiB,MAAM;AACnC,QAAI,CAAC,IAAI,SAAS,MAAM;AAAG,aAAO;EACpC;AAEA,SAAO,GAAC,KAAA,IAAI,YAAJ,OAAA,SAAA,GAAa,SAAS,MAAA;AAChC;AAEA,SAASA,kBAAiB,MAAiC;AAvF3D,MAAA;AAwFE,UAAO,KAAA,QAAA,OAAA,SAAA,KAAM,kBAAN,OAAA,KAAuB;AAChC;;;ACzFA,IAAAC,iBAAoC;AAQ7B,SAAS,kBAAkB,OAA+B;AAC/D,QAAM,EAAE,QAAQ,IAAI,IAAI;AAExB,QAAM,CAAC,SAAS,UAAU,QAAI,yBAAS,MAAM;AAC7C,QAAM,CAAC,MAAM,OAAO,QAAI,yBAAS,KAAK;AAEtC,gCAAU,MAAM;AACd,QAAI,CAAC,MAAM;AACT,iBAAW,MAAM;AACjB,cAAQ,IAAI;IACd;EACF,GAAG,CAAC,QAAQ,MAAM,OAAO,CAAC;AAE1B;IACE,MAAM,IAAI;IACV;IACA,MAAM;AACJ,iBAAW,MAAM;IACnB;EACF;AAEA,QAAM,SAAS,SAAS,QAAQ,CAAC;AAEjC,SAAO;IACL,SAAS,CAAC;IACV,aAAa;AAjCjB,UAAA;AAkCM,YAAM,MAAM,eAAe,IAAI,OAAO;AACtC,YAAM,MAAM,IAAI,IAAI,YAAY,gBAAgB,EAAE,SAAS,KAAK,CAAC;AACjE,OAAA,KAAA,IAAI,YAAJ,OAAA,SAAA,GAAa,cAAc,GAAA;IAC7B;EACF;AACF;;;ACvCA,IAAAC,iBAAkC;AA0B3B,SAAS,qBAAwB,OAAqC;AAC3E,QAAM;IACJ,OAAO;IACP;IACA;IACA,eAAe,CAAC,MAAM,SAAS,SAAS;EAC1C,IAAI;AAEJ,QAAM,eAAe,eAAe,QAAQ;AAC5C,QAAM,mBAAmB,eAAe,YAAY;AAEpD,QAAM,CAAC,mBAAmB,oBAAoB,QAAI,yBAAS,YAAiB;AAC5E,QAAM,aAAa,cAAc;AACjC,QAAM,QAAQ,aAAa,YAAY;AAEvC,QAAM,WAAW;IACf,CAAC,SAAkC;AACjC,YAAM,SAAS;AACf,YAAM,YAAY,OAAO,SAAS,aAAa,OAAO,KAAK,IAAI;AAE/D,UAAI,CAAC,iBAAiB,OAAO,SAAS,GAAG;AACvC;MACF;AAEA,UAAI,CAAC,YAAY;AACf,6BAAqB,SAAS;MAChC;AAEA,mBAAa,SAAS;IACxB;IACA,CAAC,YAAY,cAAc,OAAO,gBAAgB;EACpD;AAEA,SAAO,CAAC,OAAO,QAAQ;AACzB;;;AC3DA,SAAS,eAAe,SAAS;AAC/B,QAAM,EAAE,aAAa,SAAS,YAAY,OAAO,UAAU,IAAI;AAC/D,MAAI,CAAC;AACH,WAAO;AACT,MAAI;AACF,WAAO;AACT,MAAI,SAAS,iBAAiB;AAC5B,WAAO;AACT,SAAO;AACT;;;ACQA,IAAAC,iBAQO;AAQA,IAAM;EACX;EACA;EACA;EACA;AACF,IAAI,wBAAqC;AAMlC,IAAM,CAAC,cAAc,cAAc,IAAI,cAE5C;EACA,QAAQ;EACR,MAAM;AACR,CAAC;AAuED,SAAS,OAAO,WAAoB,UAAoB;AACtD,QAAM,cAAU,sBAAM;AACtB,QAAM,KAAK,UAAU;AACrB,aAAO,wBAAQ,MAAM;AACnB,WAAO,SAAS,IAAI,CAAC,WAAW,GAAG,MAAM,IAAI,EAAE,EAAE;EACnD,GAAG,CAAC,IAAI,QAAQ,CAAC;AACnB;AAEA,SAASC,kBAAiB,MAAiC;AAjI3D,MAAA;AAkIE,UAAO,KAAA,QAAA,OAAA,SAAA,KAAM,kBAAN,OAAA,KAAuB;AAChC;AAEA,SAASC,iBAAgB,SAAsB;AAC7C,QAAM,MAAMD,kBAAiB,OAAO;AACpC,SAAO,IAAI,kBAAmB;AAChC;AAQO,SAAS,QAAQ,QAAsB,CAAC,GAAG;AAChD,QAAM;IACJ;IACA,gBAAgB;IAChB,cAAc;IACd;IACA,aAAa;IACb;IACA,QAAQ;IACR;IACA,SAAS;IACT,QAAQ;IACR,YAAY;IACZ,eAAe;IACf;IACA,yBAAyB;IACzB,GAAG;EACL,IAAI;AAIJ,QAAM,cAAU,uBAAuB,IAAI;AAC3C,QAAM,gBAAY,uBAA0B,IAAI;AAKhD,QAAM,cAAc,mBAAmB;AAEvC,QAAM,gBAAY,4BAAY,MAAM;AAClC,0BAAsB,MAAM;AA9KhC,UAAA;AA+KM,OAAA,KAAA,QAAQ,YAAR,OAAA,SAAA,GAAiB,MAAM,EAAE,eAAe,MAAM,CAAA;IAChD,CAAC;EACH,GAAG,CAAC,CAAC;AAEL,QAAM,qBAAiB,4BAAY,MAAM;AACvC,UAAME,MAAK,WAAW,MAAM;AApLhC,UAAA;AAqLM,UAAI,iBAAiB;AACnB,SAAA,KAAA,gBAAgB,YAAhB,OAAA,SAAA,GAAyB,MAAA;MAC3B,OAAO;AACL,cAAM,QAAQ,YAAY,aAAa;AACvC,YAAI;AAAO,0BAAgB,MAAM,KAAK;MACxC;IACF,CAAC;AACD,eAAW,QAAQ,IAAIA,GAAE;EAC3B,GAAG,CAAC,aAAa,eAAe,CAAC;AAEjC,QAAM,oBAAgB,4BAAY,MAAM;AACtC,UAAMA,MAAK,WAAW,MAAM;AAC1B,YAAM,OAAO,YAAY,YAAY;AACrC,UAAI;AAAM,wBAAgB,KAAK,KAAK;IACtC,CAAC;AACD,eAAW,QAAQ,IAAIA,GAAE;EAC3B,GAAG,CAAC,WAAW,CAAC;AAEhB,QAAM,qBAAiB,4BAAY,MAAM;AACvC,kBAAA,OAAA,SAAA,WAAA;AACA,QAAI,YAAY;AACd,qBAAe;IACjB,OAAO;AACL,gBAAU;IACZ;EACF,GAAG,CAAC,YAAY,gBAAgB,WAAW,UAAU,CAAC;AAEtD,QAAM,EAAE,QAAQ,QAAQ,SAAS,SAAS,IAAI,cAAc;IAC1D,QAAQ;IACR;IACA,SAAS;IACT,QAAQ;EACV,CAAC;AAED,kBAAgB;IACd,SAAS,UAAU;IACnB,KAAK;IACL,SAAS,CAAC,UAAU;AA1NxB,UAAA;AA2NM,UAAI,GAAC,KAAA,UAAU,YAAV,OAAA,SAAA,GAAmB,SAAS,MAAM,MAAA,IAAwB;AAC7D,gBAAQ;MACV;IACF;EACF,CAAC;AAKD,QAAM,SAAc,UAAU;IAC5B,GAAG;IACH,SAAS,UAAU;IACnB;IACA;EACF,CAAC;AAED,QAAM,CAAC,cAAc,eAAe,QAAI,yBAAS,EAAE;AAKnD,kBAAgB,MAAM;AACpB,QAAI,CAAC,QAAQ;AACX,sBAAgB,EAAE;IACpB;EACF,GAAG,CAAC,MAAM,CAAC;AAEX,iBAAe,SAAS;IACtB,UAAU;IACV,SAAS;IACT,aAAa;EACf,CAAC;AAED,QAAM,iBAAiB,kBAAkB,EAAE,QAAQ,KAAK,QAAQ,CAAC;AAKjE,QAAM,CAAC,UAAU,MAAM,IAAI,OAAO,IAAI,eAAe,WAAW;AAEhE,QAAM,uBAAmB,4BAAY,MAAM;AACzC,WAAO;AACP,cAAU;EACZ,GAAG,CAAC,QAAQ,SAAS,CAAC;AAEtB,QAAM,iBAAa,uBAAiB,oBAAI,IAAI,CAAC,CAAC,CAAC;AAG/C,gCAAU,MAAM;AACd,UAAM,MAAM,WAAW;AACvB,WAAO,MAAM;AACX,UAAI,QAAQ,CAACA,QAAO,aAAaA,GAAE,CAAC;AACpC,UAAI,MAAM;IACZ;EACF,GAAG,CAAC,CAAC;AAEL,QAAM,4BAAwB,4BAAY,MAAM;AAC9C,WAAO;AACP,mBAAe;EACjB,GAAG,CAAC,gBAAgB,MAAM,CAAC;AAE3B,QAAM,2BAAuB,4BAAY,MAAM;AAC7C,WAAO;AACP,kBAAc;EAChB,GAAG,CAAC,QAAQ,aAAa,CAAC;AAE1B,QAAM,cAAU,4BAAY,MAAM;AA7RpC,QAAA,IAAA;AA8RI,UAAM,MAAMF,kBAAiB,QAAQ,OAAO;AAC5C,UAAMG,mBAAiB,KAAA,QAAQ,YAAR,OAAA,SAAA,GAAiB,SAAS,IAAI,aAAA;AACrD,UAAM,gBAAgB,UAAU,CAACA;AAEjC,QAAI,CAAC;AAAe;AAEpB,UAAM,QAAO,KAAA,YAAY,KAAK,YAAY,MAA7B,OAAA,SAAA,GAAgC;AAC7C,YAAA,OAAA,SAAA,KAAM,MAAM,EAAE,eAAe,KAAK,CAAA;EACpC,GAAG,CAAC,QAAQ,cAAc,WAAW,CAAC;AAQtC,QAAM,YAAQ,uBAAsB,IAAI;AAExC,SAAO;IACL;IACA;IACA;IACA,iBAAiB;IACjB,0BAA0B;IAC1B;IACA;IACA;IACA;IACA,aAAa,OAAO;IACpB,aAAa;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;AAiBO,SAAS,cACd,QAA4B,CAAC,GAC7B,cAA8B,MAC9B;AACA,QAAM,OAAO,eAAe;AAE5B,QAAM,EAAE,UAAU,QAAQ,uBAAuB,qBAAqB,IAAI;AAE1E,QAAM,gBAAY;IAChB,CAAC,UAA+B;AAC9B,YAAM,WAAW,MAAM;AACvB,YAAM,SAAqD;QACzD,OAAO;QACP,WAAW;QACX,SAAS;MACX;AAEA,YAAM,SAAS,OAAO,QAAQ;AAE9B,UAAI,QAAQ;AACV,cAAM,eAAe;AACrB,cAAM,gBAAgB;AACtB,eAAO,KAAK;MACd;IACF;IACA,CAAC,uBAAuB,oBAAoB;EAC9C;AAEA,SAAO;IACL,GAAG;IACH,KAAK,UAAU,KAAK,WAAW,aAAa,OAAO,YAAY;IAC/D,IAAI,KAAK;IACT,eAAe,SAAS,KAAK,MAAM;IACnC,iBAAiB,KAAK;IACtB,iBAAiB;IACjB,iBAAiB,KAAK;IACtB,SAAS,gBAAgB,MAAM,SAAS,QAAQ;IAChD,WAAW,gBAAgB,MAAM,WAAW,SAAS;EACvD;AACF;AAEA,SAAS,iBAAiB,QAA4B;AAtYtD,MAAA;AAwYE,SACEC,eAAc,MAAM,KACpB,CAAC,GAAC,KAAA,UAAA,OAAA,SAAA,OAAQ,aAAa,MAAA,MAArB,OAAA,SAAA,GAA8B,WAAW,UAAA;AAE/C;AAgBO,SAAS,YACd,QAA0B,CAAC,GAC3B,MAAsB,MACgD;AACtE,QAAM,OAAO,eAAe;AAE5B,MAAI,CAAC,MAAM;AACT,UAAM,IAAI;MACR;IACF;EACF;AAEA,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,0BAA0B;EAC5B,IAAI;AAEJ,QAAM,cAAc,0BAA0B;AAM9C,QAAM,yBAAyB,YAAY;IACzC,gBAAgB,CAAC,UACf,MAAM,QAAQ,OAAO,iBAAiB,MAAM,MAAM;EACtD,CAAC;AAED,QAAM,gBAAY;IAChB,CAAC,UAA+B;AAE9B,UAAI,CAAC,MAAM,cAAc,SAAS,MAAM,MAAiB;AAAG;AAE5D,YAAM,WAAW,MAAM;AAEvB,YAAM,SAAqD;QACzD,KAAK,CAACC,WAAUA,OAAM,eAAe;QACrC,QAAQ;QACR,WAAW,MAAM;AACf,gBAAM,OAAO,YAAY,YAAY,YAAY;AACjD,cAAI;AAAM,4BAAgB,KAAK,KAAK;QACtC;QACA,SAAS,MAAM;AACb,gBAAM,OAAO,YAAY,YAAY,YAAY;AACjD,cAAI;AAAM,4BAAgB,KAAK,KAAK;QACtC;MACF;AAEA,YAAM,KAAK,OAAO,QAAQ;AAE1B,UAAI,IAAI;AACN,cAAM,eAAe;AACrB,WAAG,KAAK;AACR;MACF;AAMA,YAAM,cAAc,uBAAuB,CAAC,cAAc;AACxD,cAAM,WAAW;UACf,YAAY,OAAO;UACnB;UACA,CAAC,SAAM;AAnejB,gBAAA,IAAA;AAmeoB,oBAAA,MAAA,KAAA,QAAA,OAAA,SAAA,KAAM,SAAN,OAAA,SAAA,GAAY,gBAAZ,OAAA,KAA2B;UAAA;UACrC,YAAY,KAAK,YAAY;QAC/B;AACA,YAAI,UAAU;AACZ,gBAAM,QAAQ,YAAY,QAAQ,SAAS,IAAI;AAC/C,0BAAgB,KAAK;QACvB;MACF,CAAC;AAED,UAAI,iBAAiB,MAAM,MAAM,GAAG;AAClC,oBAAY,KAAK;MACnB;IACF;IACA;MACE;MACA;MACA;MACA;MACA;IACF;EACF;AAEA,QAAM,oBAAgB,uBAAO,KAAK;AAClC,MAAI,QAAQ;AACV,kBAAc,UAAU;EAC1B;AAEA,QAAM,uBAAuB,eAAe;IAC1C,aAAa,cAAc;IAC3B,SAAS;IACT,MAAM;IACN,YAAY,SAAS;EACvB,CAAC;AAED,SAAO;IACL,GAAG;IACH,KAAK,UAAU,SAAS,GAAG;IAC3B,UAAU,uBAAuB,MAAM,WAAW;IAClD,UAAU;IACV,MAAM;IACN,IAAI;IACJ,OAAO;MACL,GAAG,MAAM;MACT,iBAAiB;IACnB;IACA,oBAAoB;IACpB,WAAW,gBAAgB,MAAM,WAAW,SAAS;EACvD;AACF;AAMO,SAAS,kBAAkB,QAAa,CAAC,GAAG;AACjD,QAAM,EAAE,QAAQ,OAAO,IAAI,eAAe;AAC1C,SAAO,OAAO,eAAe;IAC3B,GAAG;IACH,OAAO;MACL,YAAY,SAAS,YAAY;MACjC,GAAG,MAAM;IACX;EACF,CAAC;AACH;AA4BO,SAAS,YACd,QAA0B,CAAC,GAC3B,cAA8B,MAC9B;AACA,QAAM;IACJ,cAAc;IACd,aAAa;IACb,cAAc;IACd,SAAS;IACT,SAAS;IACT,YAAAC;IACA,aAAAC;IACA;IACA,MAAM;IACN,GAAG;EACL,IAAI;AAEJ,QAAM,OAAO,eAAe;AAE5B,QAAM;IACJ;IACA;IACA,eAAe;IACf;IACA;IACA;IACA;IACA;EACF,IAAI;AAEJ,QAAM,UAAM,uBAAuB,IAAI;AACvC,QAAM,KAAK,GAAG,MAAM,iBAAa,sBAAM,CAAC;AAKxC,QAAM,EAAE,OAAO,SAAS,IAAI,kBAAkB;IAC5C,UAAUD,eAAc,CAACC;EAC3B,CAAC;AAED,QAAM,mBAAe;IACnB,CAAC,UAAe;AACd,0BAAA,OAAA,SAAA,iBAAmB,KAAA;AACnB,UAAID;AAAY;AAChB,sBAAgB,KAAK;IACvB;IACA,CAAC,iBAAiB,OAAOA,aAAY,gBAAgB;EACvD;AAEA,QAAM,kBAAc;IAClB,CAAC,UAAe;AACd,yBAAA,OAAA,SAAA,gBAAkB,KAAA;AAClB,UAAI,IAAI,WAAW,CAACL,iBAAgB,IAAI,OAAO,GAAG;AAChD,qBAAa,KAAK;MACpB;IACF;IACA,CAAC,cAAc,eAAe;EAChC;AAEA,QAAM,mBAAe;IACnB,CAAC,UAAe;AACd,0BAAA,OAAA,SAAA,iBAAmB,KAAA;AACnB,UAAIK;AAAY;AAChB,sBAAgB,EAAE;IACpB;IACA,CAAC,iBAAiBA,aAAY,gBAAgB;EAChD;AAEA,QAAM,cAAU;IACd,CAAC,UAA4B;AAC3B,qBAAA,OAAA,SAAA,YAAc,KAAA;AACd,UAAI,CAAC,iBAAiB,MAAM,aAAa;AAAG;AAK5C,UAAI,iBAAA,OAAA,gBAAiB,mBAAmB;AACtC,gBAAQ;MACV;IACF;IACA,CAAC,SAAS,aAAa,mBAAmB,aAAa;EACzD;AAEA,QAAM,cAAU;IACd,CAAC,UAA4B;AAC3B,qBAAA,OAAA,SAAA,YAAc,KAAA;AACd,sBAAgB,KAAK;IACvB;IACA,CAAC,iBAAiB,aAAa,KAAK;EACtC;AAEA,QAAM,YAAY,UAAU;AAE5B,QAAM,gBAAgBA,eAAc,CAACC;AAErC,kBAAgB,MAAM;AACpB,QAAI,CAAC;AAAQ;AACb,QAAI,aAAa,CAAC,iBAAiB,IAAI,SAAS;AAE9C,UAAI,MAAM,SAAS;AACjB,6BAAqB,MAAM,OAAO;MACpC;AACA,YAAM,UAAU,sBAAsB,MAAM;AApqBlD,YAAA;AAqqBQ,SAAA,KAAA,IAAI,YAAJ,OAAA,SAAA,GAAa,MAAM,EAAE,eAAe,KAAK,CAAA;AACzC,cAAM,UAAU;MAClB,CAAC;IACH,WAAW,QAAQ,WAAW,CAACN,iBAAgB,QAAQ,OAAO,GAAG;AAC/D,cAAQ,QAAQ,MAAM,EAAE,eAAe,KAAK,CAAC;IAC/C;AAEA,WAAO,MAAM;AACX,UAAI,MAAM,SAAS;AACjB,6BAAqB,MAAM,OAAO;MACpC;IACF;EACF,GAAG,CAAC,WAAW,eAAe,SAAS,MAAM,CAAC;AAE9C,QAAM,iBAAiB,aAAa;IAClC;IACA;IACA;IACA;IACA;IACA,KAAK,UAAU,UAAU,KAAK,WAAW;IACzC,YAAAK;IACA,aAAAC;EACF,CAAC;AAED,SAAO;IACL,GAAG;IACH,GAAG;IACH,MAAM,YAAA,OAAA,WAAa,eAAuB;IAC1C;IACA,MAAM;IACN,UAAU,YAAY,IAAI;EAC5B;AACF;AAiBO,SAAS,cACd,QAA4B,CAAC,GAC7B,MAAsB,MACtB;AACA,QAAM,EAAE,OAAO,SAAS,WAAW,GAAG,KAAK,IAAI;AAC/C,QAAM,WAAW,YAAY,MAAM,GAAG;AACtC,SAAO;IACL,GAAG;IACH,MAAM,WAAW,IAAI;IACrB,gBAAgB;EAClB;AACF;AAcO,SAAS,mBAAmB,QAAiC,CAAC,GAAG;AACtE,QAAM;IACJ;IACA,OAAO;IACP,OAAO;IACP;IACA,UAAU;IACV,GAAG;EACL,IAAI;AAEJ,QAAM,UAAU,SAAS;AAEzB,QAAM,WAAW,UAAU,KAAK,CAAC;AAEjC,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,cAAc,gBAAA,OAAA,eAAgB;IAC9B,OAAO;IACP,UAAU;EACZ,CAAC;AAED,QAAM,eAAW;IACf,CAAC,kBAA0B;AACzB,UAAI,SAAS,WAAW,OAAO,UAAU,UAAU;AACjD,iBAAS,aAAa;MACxB;AAEA,UAAI,SAAS,cAAc,MAAM,QAAQ,KAAK,GAAG;AAC/C,cAAM,YAAY,MAAM,SAAS,aAAa,IAC1C,MAAM,OAAO,CAAC,SAAS,SAAS,aAAa,IAC7C,MAAM,OAAO,aAAa;AAE9B,iBAAS,SAAS;MACpB;IACF;IACA,CAAC,OAAO,UAAU,IAAI;EACxB;AAEA,QAAM,gBAAgB,iBAAiB,QAAQ;AAE/C,QAAM,SAAS,cAAc,IAAI,CAAC,UAAU;AAQ1C,QAAK,MAAM,KAAa,OAAO;AAAkB,aAAO;AAExD,UAAM,UAAU,CAAC,UAAsB;AAjyB3C,UAAA,IAAA;AAkyBM,eAAS,MAAM,MAAM,KAAK;AAC1B,OAAA,MAAA,KAAA,MAAM,OAAM,YAAZ,OAAA,SAAA,GAAA,KAAA,IAAsB,KAAA;IACxB;AAEA,UAAM,YACJ,SAAS,UACL,MAAM,MAAM,UAAU,QACtB,MAAM,SAAS,MAAM,MAAM,KAAK;AAEtC,eAAO,6BAAa,OAAO;MACzB;MACA;MACA;IACF,CAAC;EACH,CAAC;AAED,SAAO;IACL,GAAG;IACH,UAAU;EACZ;AACF;AAEO,SAAS,eAAe;AAC7B,QAAM,EAAE,QAAQ,QAAQ,IAAI,eAAe;AAC3C,SAAO,EAAE,QAAQ,QAAQ;AAC3B;AAEA,SAASH,eAAc,IAA4B;AA7zBnD,MAAA;AA8zBE,MAAI,CAACI,WAAU,EAAE;AAAG,WAAO;AAC3B,QAAM,OAAM,KAAA,GAAG,cAAc,gBAAjB,OAAA,KAAgC;AAC5C,SAAO,cAAc,IAAI;AAC3B;AAEA,SAASA,WAAU,IAAwB;AACzC,SACE,MAAM,QACN,OAAO,MAAM,YACb,cAAc,MACd,GAAG,aAAa,KAAK;AAEzB;;;ACj0BA,IAAAC,iBAAwB;AA+ChB,yBAAA;AAvCR,IAAM,CAAC,oBAAoB,aAAa,IAAI,cAE1C;EACA,MAAM;EACN,cAAc;AAChB,CAAC;AAoBM,IAAM,OAA4B,CAAC,UAAU;AAClD,QAAM,EAAE,SAAS,IAAI;AAErB,QAAM,SAAS,oBAAoB,QAAQ,KAAK;AAChD,QAAM,WAAW,iBAAiB,KAAK;AACvC,QAAM,EAAE,UAAU,IAAI,SAAS;AAC/B,QAAM,EAAE,aAAa,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG,UAAU,UAAU,CAAC;AAClE,QAAM,cAAU,wBAAQ,MAAM,KAAK,CAAC,GAAG,CAAC;AAExC,QAAM,EAAE,QAAQ,SAAS,YAAY,IAAI;AAEzC,aACE,wBAAC,yBAAA,EAAwB,OAAO,aAC9B,cAAA,wBAAC,cAAA,EAAa,OAAO,SACnB,cAAA,wBAAC,oBAAA,EAAmB,OAAO,QACxB,UAAA,QAAQ,UAAU,EAAE,QAAQ,SAAS,YAAY,CAAC,EAAA,CACrD,EAAA,CACF,EAAA,CACF;AAEJ;AAEA,KAAK,cAAc;;;ACvDb,IAAAC,sBAAA;AAJC,IAAM,cAAc;EACzB,CAAC,OAAO,QAAQ;AACd,UAAM,SAAS,cAAc;AAC7B,eACE;MAAC,OAAO;MAAP;QACC;QACC,GAAG;QACJ,OAAO,OAAO;QACd,WAAU;MAAA;IACZ;EAEJ;AACF;AAEA,YAAY,cAAc;;;AClB1B,IAAAC,iBAAwB;AAiClB,IAAAC,sBAAA;AA7BC,IAAM,iBAAiB;EAC5B,CAAC,OAAO,QAAQ;AACd,UAAM,EAAE,MAAM,GAAG,KAAK,IAAI;AAC1B,UAAM,SAAS,cAAc;AAO7B,UAAM,UAAU,KAAK,MAAM,OAAO,QAAA,OAAA,OAAQ,SAAY;AAEtD,UAAM,mBAAkC;MACtC,OAAO;QACL,gBAAgB;QAChB,OAAO;QACP,YAAY;QACZ,SAAS;QACT,OAAO;QACP,YAAY;QACZ,WAAW;QACX,MAAM;QACN,SAAS;QACT,GAAG,OAAO;MACZ;MACA,CAAC,OAAO,IAAI;IACd;AAEA,eACE,yBAAC,OAAO,QAAP,EAAc,KAAU,MAAM,SAAU,GAAG,MAAM,OAAO,aAAA,CAAc;EAE3E;AACF;;;AClCA,IAAAC,iBAAuD;AAqBnD,IAAAC,sBAAA;AAlBG,IAAM,WAA8C,CAAC,UAAU;AACpE,QAAM,EAAE,WAAW,UAAU,GAAG,KAAK,IAAI;AAEzC,QAAM,SAAS,cAAc;AAE7B,QAAM,QAAQ,wBAAS,KAAK,QAAQ;AAEpC,QAAM,YAAQ,+BAAe,KAAK,QAC9B,6BAAkB,OAAO;IACvB,WAAW;IACX,eAAe;IACf,WAAW,GAAG,qBAAqB,MAAM,MAAM,SAAS;EAC1D,CAAC,IACD;AAEJ,QAAM,aAAa,GAAG,6BAA6B,SAAS;AAE5D,aACE,yBAAC,OAAO,MAAP,EAAY,WAAW,YAAa,GAAG,MAAM,OAAO,OAAO,MACzD,UAAA,MAAA,CACH;AAEJ;AAEA,SAAS,cAAc;;;ACgCnB,IAAAC,sBAAA;AAfG,IAAM,WAAW,WAAoC,CAAC,OAAO,QAAQ;AAC1E,QAAM;IACJ;IACA,cAAc;IACd;IACA,iBAAiB;IACjB;IACA,GAAG;EACL,IAAI;AAEJ,QAAM,gBAAgB,YAAY,MAAM,GAAG;AAE3C,QAAM,aAAa,QAAQ;AAE3B,QAAM,YAAY,iBAChB,yBAAC,QAAA,EAAK,OAAO,EAAE,eAAe,QAAQ,MAAM,EAAE,GAAI,SAAA,CAAS,IAE3D;AAGF,aACE;IAAC;IAAA;MACE,GAAG;MACJ,WAAW,GAAG,yBAAyB,cAAc,SAAS;MAE7D,UAAA;QAAA,YACC,yBAAC,UAAA,EAAS,UAAS,SAAQ,WAAW,aACnC,UAAA,KAAA,CACH;QAED;QACA,eACC,yBAAC,aAAA,EAAY,aAAa,gBAAiB,UAAA,QAAA,CAAQ;MAAA;IAAA;EAEvD;AAEJ,CAAC;AAED,SAAS,cAAc;;;ACrBjB,IAAAC,sBAAA;AA9CN,IAAM,iBAA2B;EAC/B,OAAO;IACL,YAAY;IACZ,SAAS;IACT,OAAO;IACP,YAAY;MACV,UAAU;MACV,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;IACvB;EACF;EACA,MAAM;IACJ,eAAe;MACb,YAAY;IACd;IACA,SAAS;IACT,OAAO;IACP,YAAY;MACV,UAAU;MACV,SAAS;IACX;EACF;AACF;AAEA,IAAM,iBAAiB,OAAO,OAAO,GAAG;AAEjC,IAAM,WAAW,WAAiC,SAASC,UAChE,OACA,KACA;AA9CF,MAAA,IAAA;AA+CE,QAAM,EAAE,WAAW,aAAa,GAAG,KAAK,IAAI;AAC5C,QAAM;IACJ;IACA;IACA,0BAA0B;EAC5B,IAAI,eAAe;AAEnB,QAAM,YAAY,YAAY,MAAM,GAAG;AACvC,QAAM,kBAAkB,kBAAkB,SAAS;AAEnD,QAAM,SAAS,cAAc;AAE7B,aACE;IAAC,OAAO;IAAP;MACE,GAAG;MACJ,OAAO,EAAE,SAAQ,KAAA,MAAM,WAAN,OAAA,MAAgB,KAAA,OAAO,SAAP,OAAA,SAAA,GAAa,OAAO;MAErD,cAAA;QAAC;QAAA;UACC,UAAU;UACV,SAAS;UACT,SAAS,SAAS,UAAU;UAC5B,OAAO,EAAE,SAAS,GAAG,GAAG,OAAO,KAAK;UACnC,GAAG;UACJ,WAAW,GAAG,0BAA0B,UAAU,SAAS;UAC1D,GAAG;UACJ,UAAU;UACV,qBAAqB;YACnB,SAAS;YACT,UAAU;UACZ;QAAA;MACF;IAAA;EACF;AAEJ,CAAC;AAED,SAAS,cAAc;;;ACpEnB,IAAAC,sBAAA;AAPG,IAAM,YAAY,WAAkC,CAAC,OAAO,QAAQ;AACzE,QAAM,EAAE,OAAO,UAAU,WAAW,GAAG,KAAK,IAAI;AAEhD,QAAM,aAAa,GAAG,6BAA6B,SAAS;AAC5D,QAAM,SAAS,cAAc;AAE7B,aACE,0BAAC,OAAA,EAAI,KAAU,WAAU,sBAAqB,MAAK,SAChD,UAAA;IAAA,aACC,yBAAC,OAAO,GAAP,EAAS,WAAW,YAAa,GAAG,MAAM,OAAO,OAAO,YACtD,UAAA,MAAA,CACH;IAED;EAAA,EAAA,CACH;AAEJ,CAAC;AAED,UAAU,cAAc;;;ACZpB,IAAAC,sBAAA;AAJG,IAAM,kBAAkD,CAAC,UAAU;AACxE,QAAM,EAAE,WAAW,OAAO,GAAG,KAAK,IAAI;AACtC,QAAM,WAAW,mBAAmB,IAAI;AACxC,aACE;IAAC;IAAA;MACC;MACA,WAAW,GAAG,6BAA6B,SAAS;MACnD,GAAG;IAAA;EACN;AAEJ;AAEA,gBAAgB,cAAc;;;ACV1B,IAAAC,sBAAA;AAHJ,IAAM,mBAAmB,WAAsC,CAAC,OAAO,QAAQ;AAC7E,QAAM,SAAS,cAAc;AAC7B,aACE;IAAC,OAAO;IAAP;MACC;MACC,GAAG;MACJ,OAAO;QACL,SAAS;QACT,YAAY;QACZ,YAAY;QACZ,SAAS;QACT,GAAG,OAAO;MACZ;IAAA;EACF;AAEJ,CAAC;AAOM,IAAM,aAAa;EACxB,CAAC,OAAO,QAAQ;AACd,UAAM,EAAE,UAAU,IAAI,IAAI,GAAG,KAAK,IAAI;AAEtC,UAAM,cAAc,cAAc,MAAM,GAAG;AAE3C,UAAM,UAAU,MAAM;AAEtB,eACE;MAAC;MAAA;QACE,GAAG;QACJ,WAAW,GAAG,4BAA4B,MAAM,SAAS;QAEzD,cAAA;UAAC,OAAO;UAAP;YACC,OAAO,EAAE,eAAe,QAAQ,MAAM,YAAY,MAAM,EAAE;YAEzD,UAAA,MAAM;UAAA;QACT;MAAA;IACF;EAEJ;AACF;AAEA,WAAW,cAAc;;;AC1CrB,IAAAC,uBAAA;AAJG,IAAM,cAA0C,CAAC,UAAU;AAChE,QAAM,EAAE,WAAW,GAAG,KAAK,IAAI;AAC/B,QAAM,SAAS,cAAc;AAC7B,aACE;IAAC,OAAO;IAAP;MACC,oBAAiB;MACjB,WAAW,GAAG,wBAAwB,SAAS;MAC9C,GAAG;MACJ,OAAO,OAAO;IAAA;EAChB;AAEJ;AAEA,YAAY,cAAc;;;ACTtB,IAAAC,uBAAA;AAFJ,IAAM,YAAsC,CAAC,cAC3C,0BAAC,OAAA,EAAI,SAAQ,aAAY,OAAM,OAAM,QAAO,OAAO,GAAG,OACpD,cAAA;EAAC;EAAA;IACC,MAAK;IACL,QAAO;EAAA;AACT,EAAA,CACF;AAgBK,IAAM,iBAAiB;EAC5B,CAAC,OAAO,QAAQ;AAEd,UAAM,EAAE,MAAM,cAAc,WAAW,GAAG,KAAK,IAAI;AAEnD,UAAM,cAAc,cAAc,MAAM,GAAG;AAE3C,eACE;MAAC;MAAA;QACE,GAAG;QACJ,WAAW,GAAG,gCAAgC,KAAK,SAAS;QAE3D,UAAA;UAAA,SAAS,YACR;YAAC;YAAA;cACC,UAAS;cACT,WAAW;cACX,SAAS,MAAM,YAAY,IAAI;cAE9B,UAAA,YAAQ,0BAAC,WAAA,CAAA,CAAU;YAAA;UACtB;cAEF,0BAAC,QAAA,EAAK,OAAO,EAAE,MAAM,EAAE,GAAI,UAAA,YAAY,SAAA,CAAS;QAAA;MAAA;IAClD;EAEJ;AACF;AAEA,eAAe,KAAK;AAEpB,eAAe,cAAc;", "names": ["import_react", "import_react", "import_react", "isContentEditable", "isDisabled", "isFocusable", "import_react", "import_react", "isElement", "import_react", "import_react", "import_react", "matchWidth", "props", "getArrowStyle", "import_react", "props", "import_react", "getOwnerDocument", "import_react", "import_react", "import_react", "getOwnerDocument", "isActiveElement", "id", "hasFocusWithin", "isHTMLElement", "event", "isDisabled", "isFocusable", "isElement", "import_react", "import_jsx_runtime", "import_react", "import_jsx_runtime", "import_react", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "MenuList", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime"]}