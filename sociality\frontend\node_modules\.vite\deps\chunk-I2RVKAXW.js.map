{"version": 3, "sources": ["../../@chakra-ui/color-mode/src/color-mode-context.ts", "../../@chakra-ui/color-mode/src/color-mode.utils.ts", "../../@chakra-ui/color-mode/src/storage-manager.ts", "../../@chakra-ui/color-mode/src/color-mode-provider.tsx", "../../@chakra-ui/color-mode/src/color-mode-script.tsx"], "sourcesContent": ["import { createContext, useContext } from \"react\"\nimport { ColorModeContextType } from \"./color-mode-types\"\n\nexport const ColorModeContext = createContext({} as ColorModeContextType)\n\nColorModeContext.displayName = \"ColorModeContext\"\n\n/**\n * React hook that reads from `ColorModeProvider` context\n * Returns the color mode and function to toggle it\n */\nexport function useColorMode() {\n  const context = useContext(ColorModeContext)\n  if (context === undefined) {\n    throw new Error(\"useColorMode must be used within a ColorModeProvider\")\n  }\n  return context\n}\n\n/**\n * Change value based on color mode.\n *\n * @param light the light mode value\n * @param dark the dark mode value\n *\n * @example\n *\n * ```js\n * const Icon = useColorModeValue(MoonIcon, SunIcon)\n * ```\n */\nexport function useColorModeValue<TLight = unknown, TDark = unknown>(\n  light: TLight,\n  dark: TDark,\n) {\n  const { colorMode } = useColorMode()\n  return colorMode === \"dark\" ? dark : light\n}\n", "import { ColorMode } from \"./color-mode-types\"\n\nconst classNames = {\n  light: \"chakra-ui-light\",\n  dark: \"chakra-ui-dark\",\n}\n\ntype UtilOptions = {\n  preventTransition?: boolean\n}\n\nexport function getColorModeUtils(options: UtilOptions = {}) {\n  const { preventTransition = true } = options\n\n  const utils = {\n    setDataset: (value: ColorMode) => {\n      const cleanup = preventTransition ? utils.preventTransition() : undefined\n      document.documentElement.dataset.theme = value\n      document.documentElement.style.colorScheme = value\n      cleanup?.()\n    },\n    setClassName(dark: boolean) {\n      document.body.classList.add(dark ? classNames.dark : classNames.light)\n      document.body.classList.remove(dark ? classNames.light : classNames.dark)\n    },\n    query() {\n      return window.matchMedia(\"(prefers-color-scheme: dark)\")\n    },\n    getSystemTheme(fallback?: ColorMode) {\n      const dark = utils.query().matches ?? fallback === \"dark\"\n      return dark ? \"dark\" : \"light\"\n    },\n    addListener(fn: (cm: ColorMode) => unknown) {\n      const mql = utils.query()\n      const listener = (e: MediaQueryListEvent) => {\n        fn(e.matches ? \"dark\" : \"light\")\n      }\n\n      if (typeof mql.addListener === \"function\") mql.addListener(listener)\n      else mql.addEventListener(\"change\", listener)\n\n      return () => {\n        if (typeof mql.removeListener === \"function\")\n          mql.removeListener(listener)\n        else mql.removeEventListener(\"change\", listener)\n      }\n    },\n    preventTransition() {\n      const css = document.createElement(\"style\")\n      css.appendChild(\n        document.createTextNode(\n          `*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}`,\n        ),\n      )\n      document.head.appendChild(css)\n\n      return () => {\n        // force a reflow\n        ;(() => window.getComputedStyle(document.body))()\n\n        // wait for next tick\n        requestAnimationFrame(() => {\n          requestAnimationFrame(() => {\n            document.head.removeChild(css)\n          })\n        })\n      }\n    },\n  }\n\n  return utils\n}\n", "import { ColorMode } from \"./color-mode-types\"\n\nexport const STORAGE_KEY = \"chakra-ui-color-mode\"\n\ntype MaybeColorMode = ColorMode | undefined\n\nexport interface StorageManager {\n  type: \"cookie\" | \"localStorage\"\n  ssr?: boolean\n  get(init?: ColorMode): MaybeColorMode\n  set(value: ColorMode | \"system\"): void\n}\n\nexport function createLocalStorageManager(key: string): StorageManager {\n  return {\n    ssr: false,\n    type: \"localStorage\",\n    get(init?) {\n      if (!globalThis?.document) return init\n      let value: any\n      try {\n        value = localStorage.getItem(key) || init\n      } catch (e) {\n        // no op\n      }\n\n      return value || init\n    },\n    set(value) {\n      try {\n        localStorage.setItem(key, value)\n      } catch (e) {\n        // no op\n      }\n    },\n  }\n}\n\nexport const localStorageManager = createLocalStorageManager(STORAGE_KEY)\n\nfunction parseCookie(cookie: string, key: string): MaybeColorMode {\n  const match = cookie.match(new RegExp(`(^| )${key}=([^;]+)`))\n  return match?.[2] as MaybeColorMode\n}\n\nexport function createCookieStorageManager(\n  key: string,\n  cookie?: string,\n): StorageManager {\n  return {\n    ssr: !!cookie,\n    type: \"cookie\",\n    get(init?): MaybeColorMode {\n      if (cookie) return parseCookie(cookie, key)\n      if (!globalThis?.document) return init\n      return parseCookie(document.cookie, key) || init\n    },\n    set(value) {\n      document.cookie = `${key}=${value}; max-age=31536000; path=/`\n    },\n  }\n}\n\nexport const cookieStorageManager = createCookieStorageManager(STORAGE_KEY)\n\nexport const cookieStorageManagerSSR = (cookie: string) =>\n  createCookieStorageManager(STORAGE_KEY, cookie)\n", "import { useSafeLayoutEffect } from \"@chakra-ui/react-use-safe-layout-effect\"\nimport { useCallback, useEffect, useMemo, useState } from \"react\"\nimport { ColorModeContext } from \"./color-mode-context\"\nimport {\n  ColorMode,\n  ColorModeContextType,\n  ColorModeOptions,\n} from \"./color-mode-types\"\nimport { getColorModeUtils } from \"./color-mode.utils\"\nimport { localStorageManager, StorageManager } from \"./storage-manager\"\n\nconst noop = () => {}\n\nexport interface ColorModeProviderProps {\n  value?: ColorMode\n  children?: React.ReactNode\n  options?: ColorModeOptions\n  colorModeManager?: StorageManager\n}\n\nfunction getTheme(manager: StorageManager, fallback?: ColorMode) {\n  return manager.type === \"cookie\" && manager.ssr\n    ? manager.get(fallback)\n    : fallback\n}\n\n/**\n * Provides context for the color mode based on config in `theme`\n * Returns the color mode and function to toggle the color mode\n */\nexport function ColorModeProvider(props: ColorModeProviderProps) {\n  const {\n    value,\n    children,\n    options: {\n      useSystemColorMode,\n      initialColorMode,\n      disableTransitionOnChange,\n    } = {},\n    colorModeManager = localStorageManager,\n  } = props\n\n  const defaultColorMode = initialColorMode === \"dark\" ? \"dark\" : \"light\"\n\n  const [colorMode, rawSetColorMode] = useState(() =>\n    getTheme(colorModeManager, defaultColorMode),\n  )\n\n  const [resolvedColorMode, setResolvedColorMode] = useState(() =>\n    getTheme(colorModeManager),\n  )\n\n  const { getSystemTheme, setClassName, setDataset, addListener } = useMemo(\n    () => getColorModeUtils({ preventTransition: disableTransitionOnChange }),\n    [disableTransitionOnChange],\n  )\n\n  const resolvedValue =\n    initialColorMode === \"system\" && !colorMode ? resolvedColorMode : colorMode\n\n  const setColorMode = useCallback(\n    (value: ColorMode | \"system\") => {\n      //\n      const resolved = value === \"system\" ? getSystemTheme() : value\n      rawSetColorMode(resolved)\n\n      setClassName(resolved === \"dark\")\n      setDataset(resolved)\n\n      colorModeManager.set(resolved)\n    },\n    [colorModeManager, getSystemTheme, setClassName, setDataset],\n  )\n\n  useSafeLayoutEffect(() => {\n    if (initialColorMode === \"system\") {\n      setResolvedColorMode(getSystemTheme())\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [])\n\n  useEffect(() => {\n    const managerValue = colorModeManager.get()\n\n    if (managerValue) {\n      setColorMode(managerValue)\n      return\n    }\n\n    if (initialColorMode === \"system\") {\n      setColorMode(\"system\")\n      return\n    }\n\n    setColorMode(defaultColorMode)\n  }, [colorModeManager, defaultColorMode, initialColorMode, setColorMode])\n\n  const toggleColorMode = useCallback(() => {\n    setColorMode(resolvedValue === \"dark\" ? \"light\" : \"dark\")\n  }, [resolvedValue, setColorMode])\n\n  useEffect(() => {\n    if (!useSystemColorMode) return\n    return addListener(setColorMode)\n  }, [useSystemColorMode, addListener, setColorMode])\n\n  // presence of `value` indicates a controlled context\n  const context = useMemo(\n    () => ({\n      colorMode: value ?? (resolvedValue as ColorMode),\n      toggleColorMode: value ? noop : toggleColorMode,\n      setColorMode: value ? noop : setColorMode,\n      forced: value !== undefined,\n    }),\n    [resolvedValue, toggleColorMode, setColorMode, value],\n  )\n\n  return (\n    <ColorModeContext.Provider value={context}>\n      {children}\n    </ColorModeContext.Provider>\n  )\n}\n\nColorModeProvider.displayName = \"ColorModeProvider\"\n\n/**\n * Locks the color mode to `dark`, without any way to change it.\n */\nexport function DarkMode(props: React.PropsWithChildren<{}>) {\n  const context = useMemo<ColorModeContextType>(\n    () => ({\n      colorMode: \"dark\",\n      toggleColorMode: noop,\n      setColorMode: noop,\n      forced: true,\n    }),\n    [],\n  )\n\n  return <ColorModeContext.Provider value={context} {...props} />\n}\n\nDarkMode.displayName = \"DarkMode\"\n\n/**\n * Locks the color mode to `light` without any way to change it.\n */\nexport function LightMode(props: React.PropsWithChildren<{}>) {\n  const context = useMemo<ColorModeContextType>(\n    () => ({\n      colorMode: \"light\",\n      toggleColorMode: noop,\n      setColorMode: noop,\n      forced: true,\n    }),\n    [],\n  )\n\n  return <ColorModeContext.Provider value={context} {...props} />\n}\n\nLightMode.displayName = \"LightMode\"\n", "export type ColorModeScriptProps = {\n  type?: \"localStorage\" | \"cookie\"\n  initialColorMode?: \"light\" | \"dark\" | \"system\"\n  storageKey?: string\n  nonce?: string\n}\n\nconst VALID_VALUES = new Set([\"dark\", \"light\", \"system\"])\n\n/**\n * runtime safe-guard against invalid color mode values\n */\nfunction normalize(initialColorMode: \"light\" | \"dark\" | \"system\") {\n  let value = initialColorMode\n  if (!VALID_VALUES.has(value)) value = \"light\"\n  return value\n}\n\nexport function getScriptSrc(props: ColorModeScriptProps = {}) {\n  const {\n    initialColorMode = \"light\",\n    type = \"localStorage\",\n    storageKey: key = \"chakra-ui-color-mode\",\n  } = props\n\n  // runtime safe-guard against invalid color mode values\n  const init = normalize(initialColorMode)\n\n  const isCookie = type === \"cookie\"\n\n  const cookieScript = `(function(){try{var a=function(o){var l=\"(prefers-color-scheme: dark)\",v=window.matchMedia(l).matches?\"dark\":\"light\",e=o===\"system\"?v:o,d=document.documentElement,m=document.body,i=\"chakra-ui-light\",n=\"chakra-ui-dark\",s=e===\"dark\";return m.classList.add(s?n:i),m.classList.remove(s?i:n),d.style.colorScheme=e,d.dataset.theme=e,e},u=a,h=\"${init}\",r=\"${key}\",t=document.cookie.match(new RegExp(\"(^| )\".concat(r,\"=([^;]+)\"))),c=t?t[2]:null;c?a(c):document.cookie=\"\".concat(r,\"=\").concat(a(h),\"; max-age=31536000; path=/\")}catch(a){}})();\n  `\n\n  const localStorageScript = `(function(){try{var a=function(c){var v=\"(prefers-color-scheme: dark)\",h=window.matchMedia(v).matches?\"dark\":\"light\",r=c===\"system\"?h:c,o=document.documentElement,s=document.body,l=\"chakra-ui-light\",d=\"chakra-ui-dark\",i=r===\"dark\";return s.classList.add(i?d:l),s.classList.remove(i?l:d),o.style.colorScheme=r,o.dataset.theme=r,r},n=a,m=\"${init}\",e=\"${key}\",t=localStorage.getItem(e);t?a(t):localStorage.setItem(e,a(m))}catch(a){}})();\n  `\n\n  const fn = isCookie ? cookieScript : localStorageScript\n  return `!${fn}`.trim()\n}\n\nexport function ColorModeScript(props: ColorModeScriptProps = {}) {\n  const { nonce } = props\n\n  return (\n    <script\n      id=\"chakra-script\"\n      nonce={nonce}\n      dangerouslySetInnerHTML={{ __html: getScriptSrc(props) }}\n    />\n  )\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,mBAA0C;AAGnC,IAAM,uBAAmB,4BAAc,CAAC,CAAyB;AAExE,iBAAiB,cAAc;AAMxB,SAAS,eAAe;AAC7B,QAAM,cAAU,yBAAW,gBAAgB;AAC3C,MAAI,YAAY,QAAW;AACzB,UAAM,IAAI,MAAM,sDAAsD;EACxE;AACA,SAAO;AACT;AAcO,SAAS,kBACd,OACA,MACA;AACA,QAAM,EAAE,UAAU,IAAI,aAAa;AACnC,SAAO,cAAc,SAAS,OAAO;AACvC;;;ACnCA,IAAM,aAAa;EACjB,OAAO;EACP,MAAM;AACR;AAMO,SAAS,kBAAkB,UAAuB,CAAC,GAAG;AAC3D,QAAM,EAAE,oBAAoB,KAAK,IAAI;AAErC,QAAM,QAAQ;IACZ,YAAY,CAAC,UAAqB;AAChC,YAAM,UAAU,oBAAoB,MAAM,kBAAkB,IAAI;AAChE,eAAS,gBAAgB,QAAQ,QAAQ;AACzC,eAAS,gBAAgB,MAAM,cAAc;AAC7C,iBAAA,OAAA,SAAA,QAAA;IACF;IACA,aAAa,MAAe;AAC1B,eAAS,KAAK,UAAU,IAAI,OAAO,WAAW,OAAO,WAAW,KAAK;AACrE,eAAS,KAAK,UAAU,OAAO,OAAO,WAAW,QAAQ,WAAW,IAAI;IAC1E;IACA,QAAQ;AACN,aAAO,OAAO,WAAW,8BAA8B;IACzD;IACA,eAAe,UAAsB;AA5BzC,UAAA;AA6BM,YAAM,QAAO,KAAA,MAAM,MAAM,EAAE,YAAd,OAAA,KAAyB,aAAa;AACnD,aAAO,OAAO,SAAS;IACzB;IACA,YAAY,IAAgC;AAC1C,YAAM,MAAM,MAAM,MAAM;AACxB,YAAM,WAAW,CAAC,MAA2B;AAC3C,WAAG,EAAE,UAAU,SAAS,OAAO;MACjC;AAEA,UAAI,OAAO,IAAI,gBAAgB;AAAY,YAAI,YAAY,QAAQ;;AAC9D,YAAI,iBAAiB,UAAU,QAAQ;AAE5C,aAAO,MAAM;AACX,YAAI,OAAO,IAAI,mBAAmB;AAChC,cAAI,eAAe,QAAQ;;AACxB,cAAI,oBAAoB,UAAU,QAAQ;MACjD;IACF;IACA,oBAAoB;AAClB,YAAM,MAAM,SAAS,cAAc,OAAO;AAC1C,UAAI;QACF,SAAS;UACP;QACF;MACF;AACA,eAAS,KAAK,YAAY,GAAG;AAE7B,aAAO,MAAM;AAEX;AAAC,SAAC,MAAM,OAAO,iBAAiB,SAAS,IAAI,GAAG;AAGhD,8BAAsB,MAAM;AAC1B,gCAAsB,MAAM;AAC1B,qBAAS,KAAK,YAAY,GAAG;UAC/B,CAAC;QACH,CAAC;MACH;IACF;EACF;AAEA,SAAO;AACT;;;ACrEO,IAAM,cAAc;AAWpB,SAAS,0BAA0B,KAA6B;AACrE,SAAO;IACL,KAAK;IACL,MAAM;IACN,IAAI,MAAO;AACT,UAAI,EAAC,cAAA,OAAA,SAAA,WAAY;AAAU,eAAO;AAClC,UAAI;AACJ,UAAI;AACF,gBAAQ,aAAa,QAAQ,GAAG,KAAK;MACvC,SAAS,GAAP;MAEF;AAEA,aAAO,SAAS;IAClB;IACA,IAAI,OAAO;AACT,UAAI;AACF,qBAAa,QAAQ,KAAK,KAAK;MACjC,SAAS,GAAP;MAEF;IACF;EACF;AACF;AAEO,IAAM,sBAAsB,0BAA0B,WAAW;AAExE,SAAS,YAAY,QAAgB,KAA6B;AAChE,QAAM,QAAQ,OAAO,MAAM,IAAI,OAAO,QAAQ,GAAA,UAAa,CAAC;AAC5D,SAAO,SAAA,OAAA,SAAA,MAAQ,CAAA;AACjB;AAEO,SAAS,2BACd,KACA,QACgB;AAChB,SAAO;IACL,KAAK,CAAC,CAAC;IACP,MAAM;IACN,IAAI,MAAuB;AACzB,UAAI;AAAQ,eAAO,YAAY,QAAQ,GAAG;AAC1C,UAAI,EAAC,cAAA,OAAA,SAAA,WAAY;AAAU,eAAO;AAClC,aAAO,YAAY,SAAS,QAAQ,GAAG,KAAK;IAC9C;IACA,IAAI,OAAO;AACT,eAAS,SAAS,GAAG,GAAA,IAAO,KAAA;IAC9B;EACF;AACF;AAEO,IAAM,uBAAuB,2BAA2B,WAAW;AAEnE,IAAM,0BAA0B,CAAC,WACtC,2BAA2B,aAAa,MAAM;;;ACjEhD,IAAAA,gBAA0D;AAqHtD,yBAAA;AA3GJ,IAAM,OAAO,MAAM;AAAC;AASpB,SAAS,SAAS,SAAyB,UAAsB;AAC/D,SAAO,QAAQ,SAAS,YAAY,QAAQ,MACxC,QAAQ,IAAI,QAAQ,IACpB;AACN;AAMO,SAAS,kBAAkB,OAA+B;AAC/D,QAAM;IACJ;IACA;IACA,SAAS;MACP;MACA;MACA;IACF,IAAI,CAAC;IACL,mBAAmB;EACrB,IAAI;AAEJ,QAAM,mBAAmB,qBAAqB,SAAS,SAAS;AAEhE,QAAM,CAAC,WAAW,eAAe,QAAI;IAAS,MAC5C,SAAS,kBAAkB,gBAAgB;EAC7C;AAEA,QAAM,CAAC,mBAAmB,oBAAoB,QAAI;IAAS,MACzD,SAAS,gBAAgB;EAC3B;AAEA,QAAM,EAAE,gBAAgB,cAAc,YAAY,YAAY,QAAI;IAChE,MAAM,kBAAkB,EAAE,mBAAmB,0BAA0B,CAAC;IACxE,CAAC,yBAAyB;EAC5B;AAEA,QAAM,gBACJ,qBAAqB,YAAY,CAAC,YAAY,oBAAoB;AAEpE,QAAM,mBAAe;IACnB,CAACC,WAAgC;AAE/B,YAAM,WAAWA,WAAU,WAAW,eAAe,IAAIA;AACzD,sBAAgB,QAAQ;AAExB,mBAAa,aAAa,MAAM;AAChC,iBAAW,QAAQ;AAEnB,uBAAiB,IAAI,QAAQ;IAC/B;IACA,CAAC,kBAAkB,gBAAgB,cAAc,UAAU;EAC7D;AAEA,sBAAoB,MAAM;AACxB,QAAI,qBAAqB,UAAU;AACjC,2BAAqB,eAAe,CAAC;IACvC;EAEF,GAAG,CAAC,CAAC;AAEL,+BAAU,MAAM;AACd,UAAM,eAAe,iBAAiB,IAAI;AAE1C,QAAI,cAAc;AAChB,mBAAa,YAAY;AACzB;IACF;AAEA,QAAI,qBAAqB,UAAU;AACjC,mBAAa,QAAQ;AACrB;IACF;AAEA,iBAAa,gBAAgB;EAC/B,GAAG,CAAC,kBAAkB,kBAAkB,kBAAkB,YAAY,CAAC;AAEvE,QAAM,sBAAkB,2BAAY,MAAM;AACxC,iBAAa,kBAAkB,SAAS,UAAU,MAAM;EAC1D,GAAG,CAAC,eAAe,YAAY,CAAC;AAEhC,+BAAU,MAAM;AACd,QAAI,CAAC;AAAoB;AACzB,WAAO,YAAY,YAAY;EACjC,GAAG,CAAC,oBAAoB,aAAa,YAAY,CAAC;AAGlD,QAAM,cAAU;IACd,OAAO;MACL,WAAW,SAAA,OAAA,QAAU;MACrB,iBAAiB,QAAQ,OAAO;MAChC,cAAc,QAAQ,OAAO;MAC7B,QAAQ,UAAU;IACpB;IACA,CAAC,eAAe,iBAAiB,cAAc,KAAK;EACtD;AAEA,aACE,wBAAC,iBAAiB,UAAjB,EAA0B,OAAO,SAC/B,SAAA,CACH;AAEJ;AAEA,kBAAkB,cAAc;AAKzB,SAAS,SAAS,OAAoC;AAC3D,QAAM,cAAU;IACd,OAAO;MACL,WAAW;MACX,iBAAiB;MACjB,cAAc;MACd,QAAQ;IACV;IACA,CAAC;EACH;AAEA,aAAO,wBAAC,iBAAiB,UAAjB,EAA0B,OAAO,SAAU,GAAG,MAAA,CAAO;AAC/D;AAEA,SAAS,cAAc;AAKhB,SAAS,UAAU,OAAoC;AAC5D,QAAM,cAAU;IACd,OAAO;MACL,WAAW;MACX,iBAAiB;MACjB,cAAc;MACd,QAAQ;IACV;IACA,CAAC;EACH;AAEA,aAAO,wBAAC,iBAAiB,UAAjB,EAA0B,OAAO,SAAU,GAAG,MAAA,CAAO;AAC/D;AAEA,UAAU,cAAc;;;ACtHpB,IAAAC,sBAAA;AArCJ,IAAM,eAAe,oBAAI,IAAI,CAAC,QAAQ,SAAS,QAAQ,CAAC;AAKxD,SAAS,UAAU,kBAA+C;AAChE,MAAI,QAAQ;AACZ,MAAI,CAAC,aAAa,IAAI,KAAK;AAAG,YAAQ;AACtC,SAAO;AACT;AAEO,SAAS,aAAa,QAA8B,CAAC,GAAG;AAC7D,QAAM;IACJ,mBAAmB;IACnB,OAAO;IACP,YAAY,MAAM;EACpB,IAAI;AAGJ,QAAM,OAAO,UAAU,gBAAgB;AAEvC,QAAM,WAAW,SAAS;AAE1B,QAAM,eAAe,oVAAoV,IAAA,QAAY,GAAA;;AAGrX,QAAM,qBAAqB,oVAAoV,IAAA,QAAY,GAAA;;AAG3X,QAAM,KAAK,WAAW,eAAe;AACrC,SAAO,IAAI,EAAA,GAAK,KAAK;AACvB;AAEO,SAAS,gBAAgB,QAA8B,CAAC,GAAG;AAChE,QAAM,EAAE,MAAM,IAAI;AAElB,aACE;IAAC;IAAA;MACC,IAAG;MACH;MACA,yBAAyB,EAAE,QAAQ,aAAa,KAAK,EAAE;IAAA;EACzD;AAEJ;", "names": ["import_react", "value", "import_jsx_runtime"]}