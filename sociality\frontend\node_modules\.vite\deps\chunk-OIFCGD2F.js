import {
  require_react
} from "./chunk-LXGCQ6UQ.js";
import {
  __toESM
} from "./chunk-ROME4SDB.js";

// node_modules/@chakra-ui/react-use-safe-layout-effect/dist/index.mjs
var import_react = __toESM(require_react(), 1);
var useSafeLayoutEffect = Boolean(globalThis == null ? void 0 : globalThis.document) ? import_react.useLayoutEffect : import_react.useEffect;

export {
  useSafeLayoutEffect
};
//# sourceMappingURL=chunk-OIFCGD2F.js.map
