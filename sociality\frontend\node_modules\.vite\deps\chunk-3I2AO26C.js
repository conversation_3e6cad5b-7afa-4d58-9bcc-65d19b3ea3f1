import {
  css,
  isStyleProp,
  propN<PERSON>s,
  resolveStyleConfig,
  toCSSVar
} from "./chunk-CV2Y4IOD.js";
import {
  Global,
  ThemeContext,
  ThemeProvider,
  require_react_fast_compare,
  styled
} from "./chunk-FUQXZUED.js";
import {
  require_lodash
} from "./chunk-SV2WXQL6.js";
import {
  useColorMode
} from "./chunk-I2RVKAXW.js";
import {
  require_jsx_runtime
} from "./chunk-VZBRM2AZ.js";
import {
  require_react
} from "./chunk-LXGCQ6UQ.js";
import {
  __toESM
} from "./chunk-ROME4SDB.js";

// node_modules/@chakra-ui/system/dist/chunk-UIGT7YZF.mjs
var import_react2 = __toESM(require_react(), 1);
function useTheme() {
  const theme = (0, import_react2.useContext)(
    ThemeContext
  );
  if (!theme) {
    throw Error(
      "useTheme: `theme` is undefined. Seems you forgot to wrap your app in `<ChakraProvider />` or `<ThemeProvider />`"
    );
  }
  return theme;
}

// node_modules/@chakra-ui/system/dist/chunk-7FWEOSAE.mjs
function useChakra() {
  const colorModeResult = useColorMode();
  const theme = useTheme();
  return { ...colorModeResult, theme };
}
function getBreakpointValue(theme, value, fallback) {
  var _a2, _b;
  if (value == null)
    return value;
  const getValue = (val) => {
    var _a22, _b2;
    return (_b2 = (_a22 = theme.__breakpoints) == null ? void 0 : _a22.asArray) == null ? void 0 : _b2[val];
  };
  return (_b = (_a2 = getValue(value)) != null ? _a2 : getValue(fallback)) != null ? _b : fallback;
}
function getTokenValue(theme, value, fallback) {
  var _a2, _b;
  if (value == null)
    return value;
  const getValue = (val) => {
    var _a22, _b2;
    return (_b2 = (_a22 = theme.__cssMap) == null ? void 0 : _a22[val]) == null ? void 0 : _b2.value;
  };
  return (_b = (_a2 = getValue(value)) != null ? _a2 : getValue(fallback)) != null ? _b : fallback;
}
function getToken(scale, token, fallback) {
  const _token = Array.isArray(token) ? token : [token];
  const _fallback = Array.isArray(fallback) ? fallback : [fallback];
  return (theme) => {
    const fallbackArr = _fallback.filter(Boolean);
    const result = _token.map((token2, index) => {
      var _a2, _b;
      if (scale === "breakpoints") {
        return getBreakpointValue(theme, token2, (_a2 = fallbackArr[index]) != null ? _a2 : token2);
      }
      const path = `${scale}.${token2}`;
      return getTokenValue(theme, path, (_b = fallbackArr[index]) != null ? _b : token2);
    });
    return Array.isArray(token) ? result : result[0];
  };
}

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-O3SWHQEE.mjs
function canUseDOM() {
  return !!(typeof window !== "undefined" && window.document && window.document.createElement);
}
var isBrowser = canUseDOM();

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-YTQ3XZ3T.mjs
var import_lodash = __toESM(require_lodash(), 1);
function omit(object, keys) {
  const result = {};
  Object.keys(object).forEach((key) => {
    if (keys.includes(key))
      return;
    result[key] = object[key];
  });
  return result;
}
function get(obj, path, fallback, index) {
  const key = typeof path === "string" ? path.split(".") : [path];
  for (index = 0; index < key.length; index += 1) {
    if (!obj)
      break;
    obj = obj[key[index]];
  }
  return obj === void 0 ? fallback : obj;
}
var memoize = (fn) => {
  const cache = /* @__PURE__ */ new WeakMap();
  const memoizedFn = (obj, path, fallback, index) => {
    if (typeof obj === "undefined") {
      return fn(obj, path, fallback);
    }
    if (!cache.has(obj)) {
      cache.set(obj, /* @__PURE__ */ new Map());
    }
    const map = cache.get(obj);
    if (map.has(path)) {
      return map.get(path);
    }
    const value = fn(obj, path, fallback, index);
    map.set(path, value);
    return value;
  };
  return memoizedFn;
};
var memoizedGet = memoize(get);
function objectFilter(object, fn) {
  const result = {};
  Object.keys(object).forEach((key) => {
    const value = object[key];
    const shouldPass = fn(value, key, object);
    if (shouldPass) {
      result[key] = value;
    }
  });
  return result;
}
var filterUndefined = (object) => objectFilter(object, (val) => val !== null && val !== void 0);

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-Y5FGD7DM.mjs
function isFunction(value) {
  return typeof value === "function";
}
var __DEV__ = true;

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-M3TFMUOL.mjs
function runIfFn(valueOrFn, ...args) {
  return isFunction(valueOrFn) ? valueOrFn(...args) : valueOrFn;
}
function once(fn) {
  let result;
  return function func(...args) {
    if (fn) {
      result = fn.apply(this, args);
      fn = null;
    }
    return result;
  };
}
var warn = once((options) => () => {
  const { condition, message } = options;
  if (condition && __DEV__) {
    console.warn(message);
  }
});
var error = once((options) => () => {
  const { condition, message } = options;
  if (condition && __DEV__) {
    console.error(message);
  }
});

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-YAFHXCR4.mjs
var minSafeInteger = Number.MIN_SAFE_INTEGER || -9007199254740991;
var maxSafeInteger = Number.MAX_SAFE_INTEGER || 9007199254740991;

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-FGAEJGLB.mjs
var breakpoints = Object.freeze([
  "base",
  "sm",
  "md",
  "lg",
  "xl",
  "2xl"
]);

// node_modules/@chakra-ui/system/node_modules/@chakra-ui/utils/dist/chunk-5LUSIWEA.mjs
var focusableElList = [
  "input:not(:disabled):not([disabled])",
  "select:not(:disabled):not([disabled])",
  "textarea:not(:disabled):not([disabled])",
  "embed",
  "iframe",
  "object",
  "a[href]",
  "area[href]",
  "button:not(:disabled):not([disabled])",
  "[tabindex]",
  "audio[controls]",
  "video[controls]",
  "*[tabindex]:not([aria-disabled])",
  "*[contenteditable]"
];
var focusableElSelector = focusableElList.join();

// node_modules/@chakra-ui/system/dist/chunk-DMO4EI7P.mjs
var import_react3 = __toESM(require_react(), 1);
var import_react_fast_compare = __toESM(require_react_fast_compare(), 1);
function useStyleConfigImpl(themeKey, props = {}) {
  var _a2;
  const { styleConfig: styleConfigProp, ...rest } = props;
  const { theme, colorMode } = useChakra();
  const themeStyleConfig = themeKey ? memoizedGet(theme, `components.${themeKey}`) : void 0;
  const styleConfig = styleConfigProp || themeStyleConfig;
  const mergedProps = (0, import_lodash.default)(
    { theme, colorMode },
    (_a2 = styleConfig == null ? void 0 : styleConfig.defaultProps) != null ? _a2 : {},
    filterUndefined(omit(rest, ["children"]))
  );
  const stylesRef = (0, import_react3.useRef)({});
  if (styleConfig) {
    const getStyles = resolveStyleConfig(styleConfig);
    const styles = getStyles(mergedProps);
    const isStyleEqual = (0, import_react_fast_compare.default)(stylesRef.current, styles);
    if (!isStyleEqual) {
      stylesRef.current = styles;
    }
  }
  return stylesRef.current;
}
function useStyleConfig(themeKey, props = {}) {
  return useStyleConfigImpl(themeKey, props);
}
function useMultiStyleConfig(themeKey, props = {}) {
  return useStyleConfigImpl(themeKey, props);
}

// node_modules/@chakra-ui/object-utils/dist/chunk-R3DH46PF.mjs
function compact(object) {
  const clone = Object.assign({}, object);
  for (let key in clone) {
    if (clone[key] === void 0)
      delete clone[key];
  }
  return clone;
}

// node_modules/@chakra-ui/object-utils/dist/chunk-OLTBUDV5.mjs
function assignAfter(target, ...sources) {
  if (target == null) {
    throw new TypeError("Cannot convert undefined or null to object");
  }
  const result = { ...target };
  for (const nextSource of sources) {
    if (nextSource == null)
      continue;
    for (const nextKey in nextSource) {
      if (!Object.prototype.hasOwnProperty.call(nextSource, nextKey))
        continue;
      if (nextKey in result)
        delete result[nextKey];
      result[nextKey] = nextSource[nextKey];
    }
  }
  return result;
}

// node_modules/@chakra-ui/system/dist/chunk-FDQH4LQI.mjs
var allPropNames = /* @__PURE__ */ new Set([
  ...propNames,
  "textStyle",
  "layerStyle",
  "apply",
  "noOfLines",
  "focusBorderColor",
  "errorBorderColor",
  "as",
  "__css",
  "css",
  "sx"
]);
var validHTMLProps = /* @__PURE__ */ new Set([
  "htmlWidth",
  "htmlHeight",
  "htmlSize",
  "htmlTranslate"
]);
function shouldForwardProp(prop) {
  return validHTMLProps.has(prop) || !allPropNames.has(prop);
}

// node_modules/@chakra-ui/system/dist/chunk-5PL47M24.mjs
var import_react4 = __toESM(require_react(), 1);
var _a;
var emotion_styled = (_a = styled.default) != null ? _a : styled;
var toCSSObject = ({ baseStyle }) => (props) => {
  const { theme, css: cssProp, __css, sx, ...rest } = props;
  const styleProps = objectFilter(rest, (_, prop) => isStyleProp(prop));
  const finalBaseStyle = runIfFn(baseStyle, props);
  const finalStyles = assignAfter(
    {},
    __css,
    finalBaseStyle,
    filterUndefined(styleProps),
    sx
  );
  const computedCSS = css(finalStyles)(props.theme);
  return cssProp ? [computedCSS, cssProp] : computedCSS;
};
function styled2(component, options) {
  const { baseStyle, ...styledOptions } = options != null ? options : {};
  if (!styledOptions.shouldForwardProp) {
    styledOptions.shouldForwardProp = shouldForwardProp;
  }
  const styleObject = toCSSObject({ baseStyle });
  const Component = emotion_styled(
    component,
    styledOptions
  )(styleObject);
  const chakraComponent = import_react4.default.forwardRef(function ChakraComponent(props, ref) {
    const { colorMode, forced } = useColorMode();
    return import_react4.default.createElement(Component, {
      ref,
      "data-theme": forced ? colorMode : void 0,
      ...props
    });
  });
  return chakraComponent;
}

// node_modules/@chakra-ui/system/dist/chunk-ZHQNHOQS.mjs
function factory() {
  const cache = /* @__PURE__ */ new Map();
  return new Proxy(styled2, {
    /**
     * @example
     * const Div = chakra("div")
     * const WithChakra = chakra(AnotherComponent)
     */
    apply(target, thisArg, argArray) {
      return styled2(...argArray);
    },
    /**
     * @example
     * <chakra.div />
     */
    get(_, element) {
      if (!cache.has(element)) {
        cache.set(element, styled2(element));
      }
      return cache.get(element);
    }
  });
}
var chakra = factory();

// node_modules/@chakra-ui/system/dist/chunk-ZJJGQIVY.mjs
var import_react5 = __toESM(require_react(), 1);
function forwardRef(component) {
  return (0, import_react5.forwardRef)(component);
}

// node_modules/@chakra-ui/react-utils/dist/chunk-IH2MM24A.mjs
var import_react6 = __toESM(require_react(), 1);

// node_modules/@chakra-ui/react-utils/dist/chunk-ITIKTQWJ.mjs
var import_react7 = __toESM(require_react(), 1);
function createContext(options = {}) {
  const {
    strict = true,
    errorMessage = "useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",
    name
  } = options;
  const Context = (0, import_react7.createContext)(void 0);
  Context.displayName = name;
  function useContext2() {
    var _a2;
    const context = (0, import_react7.useContext)(Context);
    if (!context && strict) {
      const error3 = new Error(errorMessage);
      error3.name = "ContextError";
      (_a2 = Error.captureStackTrace) == null ? void 0 : _a2.call(Error, error3, useContext2);
      throw error3;
    }
    return context;
  }
  return [
    Context.Provider,
    useContext2,
    Context
  ];
}

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-O3SWHQEE.mjs
function canUseDOM2() {
  return !!(typeof window !== "undefined" && window.document && window.document.createElement);
}
var isBrowser2 = canUseDOM2();

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-YTQ3XZ3T.mjs
var import_lodash2 = __toESM(require_lodash(), 1);
function get2(obj, path, fallback, index) {
  const key = typeof path === "string" ? path.split(".") : [path];
  for (index = 0; index < key.length; index += 1) {
    if (!obj)
      break;
    obj = obj[key[index]];
  }
  return obj === void 0 ? fallback : obj;
}
var memoize2 = (fn) => {
  const cache = /* @__PURE__ */ new WeakMap();
  const memoizedFn = (obj, path, fallback, index) => {
    if (typeof obj === "undefined") {
      return fn(obj, path, fallback);
    }
    if (!cache.has(obj)) {
      cache.set(obj, /* @__PURE__ */ new Map());
    }
    const map = cache.get(obj);
    if (map.has(path)) {
      return map.get(path);
    }
    const value = fn(obj, path, fallback, index);
    map.set(path, value);
    return value;
  };
  return memoizedFn;
};
var memoizedGet2 = memoize2(get2);

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-Y5FGD7DM.mjs
var __DEV__2 = true;

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-M3TFMUOL.mjs
function once2(fn) {
  let result;
  return function func(...args) {
    if (fn) {
      result = fn.apply(this, args);
      fn = null;
    }
    return result;
  };
}
var warn2 = once2((options) => () => {
  const { condition, message } = options;
  if (condition && __DEV__2) {
    console.warn(message);
  }
});
var error2 = once2((options) => () => {
  const { condition, message } = options;
  if (condition && __DEV__2) {
    console.error(message);
  }
});

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-YAFHXCR4.mjs
var minSafeInteger2 = Number.MIN_SAFE_INTEGER || -9007199254740991;
var maxSafeInteger2 = Number.MAX_SAFE_INTEGER || 9007199254740991;

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-FGAEJGLB.mjs
var breakpoints2 = Object.freeze([
  "base",
  "sm",
  "md",
  "lg",
  "xl",
  "2xl"
]);

// node_modules/@chakra-ui/react-utils/node_modules/@chakra-ui/utils/dist/chunk-5LUSIWEA.mjs
var focusableElList2 = [
  "input:not(:disabled):not([disabled])",
  "select:not(:disabled):not([disabled])",
  "textarea:not(:disabled):not([disabled])",
  "embed",
  "iframe",
  "object",
  "a[href]",
  "area[href]",
  "button:not(:disabled):not([disabled])",
  "[tabindex]",
  "audio[controls]",
  "video[controls]",
  "*[tabindex]:not([aria-disabled])",
  "*[contenteditable]"
];
var focusableElSelector2 = focusableElList2.join();

// node_modules/@chakra-ui/system/dist/chunk-MFVQSVQB.mjs
var import_react9 = __toESM(require_react(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
function ThemeProvider2(props) {
  const { cssVarsRoot, theme, children } = props;
  const computedTheme = (0, import_react9.useMemo)(() => toCSSVar(theme), [theme]);
  return (0, import_jsx_runtime.jsxs)(ThemeProvider, { theme: computedTheme, children: [
    (0, import_jsx_runtime.jsx)(CSSVars, { root: cssVarsRoot }),
    children
  ] });
}
function CSSVars({ root = ":host, :root" }) {
  const selector = [root, `[data-theme]`].join(",");
  return (0, import_jsx_runtime.jsx)(Global, { styles: (theme) => ({ [selector]: theme.__cssVars }) });
}
var [StylesProvider, useStyles] = createContext({
  name: "StylesContext",
  errorMessage: "useStyles: `styles` is undefined. Seems you forgot to wrap the components in `<StylesProvider />` "
});

export {
  useTheme,
  useChakra,
  getToken,
  useStyleConfig,
  useMultiStyleConfig,
  compact,
  chakra,
  forwardRef,
  ThemeProvider2 as ThemeProvider
};
//# sourceMappingURL=chunk-3I2AO26C.js.map
