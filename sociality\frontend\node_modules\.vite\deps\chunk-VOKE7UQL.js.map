{"version": 3, "sources": ["../../@chakra-ui/react-use-update-effect/src/index.ts", "../../@chakra-ui/react-use-callback-ref/src/index.ts"], "sourcesContent": ["import { useEffect, useRef } from \"react\"\n\nexport function useUpdateEffect(\n  callback: React.EffectCallback,\n  deps: React.DependencyList,\n) {\n  const renderCycleRef = useRef(false)\n  const effectCycleRef = useRef(false)\n\n  useEffect(() => {\n    const mounted = renderCycleRef.current\n    const run = mounted && effectCycleRef.current\n    if (run) {\n      return callback()\n    }\n    effectCycleRef.current = true\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps)\n\n  useEffect(() => {\n    renderCycleRef.current = true\n    return () => {\n      renderCycleRef.current = false\n    }\n  }, [])\n}\n", "import { useCallback, useEffect, useRef } from \"react\"\n\nexport function useCallbackRef<T extends (...args: any[]) => any>(\n  callback: T | undefined,\n  deps: React.DependencyList = [],\n) {\n  const callbackRef = useRef(callback)\n\n  useEffect(() => {\n    callbackRef.current = callback\n  })\n\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  return useCallback(((...args) => callbackRef.current?.(...args)) as T, deps)\n}\n"], "mappings": ";;;;;;;;AAAA,mBAAkC;AAE3B,SAAS,gBACd,UACA,MACA;AACA,QAAM,qBAAiB,qBAAO,KAAK;AACnC,QAAM,qBAAiB,qBAAO,KAAK;AAEnC,8BAAU,MAAM;AACd,UAAM,UAAU,eAAe;AAC/B,UAAM,MAAM,WAAW,eAAe;AACtC,QAAI,KAAK;AACP,aAAO,SAAS;IAClB;AACA,mBAAe,UAAU;EAE3B,GAAG,IAAI;AAEP,8BAAU,MAAM;AACd,mBAAe,UAAU;AACzB,WAAO,MAAM;AACX,qBAAe,UAAU;IAC3B;EACF,GAAG,CAAC,CAAC;AACP;;;ACzBA,IAAAA,gBAA+C;AAExC,SAAS,eACd,UACA,OAA6B,CAAC,GAC9B;AACA,QAAM,kBAAc,sBAAO,QAAQ;AAEnC,+BAAU,MAAM;AACd,gBAAY,UAAU;EACxB,CAAC;AAGD,aAAO,2BAAa,IAAI,SAAM;AAbhC,QAAA;AAamC,YAAA,KAAA,YAAY,YAAZ,OAAA,SAAA,GAAA,KAAA,aAAsB,GAAG,IAAA;EAAA,GAAa,IAAI;AAC7E;", "names": ["import_react"]}