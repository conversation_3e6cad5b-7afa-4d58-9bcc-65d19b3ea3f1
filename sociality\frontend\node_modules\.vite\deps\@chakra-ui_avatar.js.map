{"version": 3, "sources": ["../../@chakra-ui/avatar/src/avatar-context.tsx", "../../@chakra-ui/avatar/src/avatar-badge.tsx", "../../@chakra-ui/avatar/src/avatar-name.tsx", "../../@chakra-ui/avatar/src/generic-avatar-icon.tsx", "../../@chakra-ui/avatar/src/avatar-image.tsx", "../../@chakra-ui/avatar/src/avatar.tsx", "../../@chakra-ui/avatar/src/avatar-group.tsx", "../../utilities/object-utils/src/compact.ts"], "sourcesContent": ["import { createContext } from \"@chakra-ui/react-context\"\nimport { SystemStyleObject } from \"@chakra-ui/system\"\n\nexport const [AvatarStylesProvider, useAvatarStyles] = createContext<\n  Record<string, SystemStyleObject>\n>({\n  name: `AvatarStylesContext`,\n  hookName: `useAvatarStyles`,\n  providerName: \"<Avatar/>\",\n})\n", "import {\n  chakra,\n  forwardRef,\n  HTMLChakraProps,\n  SystemStyleObject,\n} from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\nimport { useAvatarStyles } from \"./avatar-context\"\n\ntype BadgePlacement = \"top-start\" | \"top-end\" | \"bottom-start\" | \"bottom-end\"\n\nconst placementMap: Record<BadgePlacement, SystemStyleObject> = {\n  \"top-start\": {\n    top: \"0\",\n    insetStart: \"0\",\n    transform: \"translate(-25%, -25%)\",\n  },\n  \"top-end\": {\n    top: \"0\",\n    insetEnd: \"0\",\n    transform: \"translate(25%, -25%)\",\n  },\n  \"bottom-start\": {\n    bottom: \"0\",\n    insetStart: \"0\",\n    transform: \"translate(-25%, 25%)\",\n  },\n  \"bottom-end\": {\n    bottom: \"0\",\n    insetEnd: \"0\",\n    transform: \"translate(25%, 25%)\",\n  },\n}\n\nexport interface AvatarBadgeProps extends HTMLChakraProps<\"div\"> {\n  placement?: BadgePlacement\n}\n\n/**\n * AvatarBadge used to show extra badge to the top-right\n * or bottom-right corner of an avatar.\n */\nexport const AvatarBadge = forwardRef<AvatarBadgeProps, \"div\">(\n  function AvatarBadge(props, ref) {\n    const { placement = \"bottom-end\", className, ...rest } = props\n    const styles = useAvatarStyles()\n\n    const placementStyles = placementMap[placement]\n\n    const badgeStyles: SystemStyleObject = {\n      position: \"absolute\",\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      ...placementStyles,\n      ...styles.badge,\n    }\n\n    return (\n      <chakra.div\n        ref={ref}\n        {...rest}\n        className={cx(\"chakra-avatar__badge\", className)}\n        __css={badgeStyles}\n      />\n    )\n  },\n)\n\nAvatarBadge.displayName = \"AvatarBadge\"\n", "import { chakra, HTMLChakraProps } from \"@chakra-ui/system\"\nimport { useAvatarStyles } from \"./avatar-context\"\nimport { AvatarOptions } from \"./avatar-types\"\n\nexport function initials(name: string) {\n  const names = name.split(\" \")\n  const firstName = names[0] ?? \"\"\n  const lastName = names.length > 1 ? names[names.length - 1] : \"\"\n  return firstName && lastName\n    ? `${firstName.charAt(0)}${lastName.charAt(0)}`\n    : firstName.charAt(0)\n}\n\ninterface AvatarNameProps\n  extends HTMLChakraProps<\"div\">,\n    Pick<AvatarOptions, \"name\" | \"getInitials\"> {}\n/**\n * The avatar name container\n */\n\nexport function AvatarName(props: AvatarNameProps) {\n  const { name, getInitials, ...rest } = props\n  const styles = useAvatarStyles()\n\n  return (\n    <chakra.div role=\"img\" aria-label={name} {...rest} __css={styles.label}>\n      {name ? getInitials?.(name) : null}\n    </chakra.div>\n  )\n}\n\nAvatarName.displayName = \"AvatarName\"\n", "import { chakra, ChakraComponent } from \"@chakra-ui/system\"\n\n/**\n * Fallback avatar react component.\n * This should be a generic svg used to represent an avatar\n */\nexport const GenericAvatarIcon: ChakraComponent<\"svg\"> = (props) => (\n  <chakra.svg\n    viewBox=\"0 0 128 128\"\n    color=\"#fff\"\n    width=\"100%\"\n    height=\"100%\"\n    className=\"chakra-avatar__svg\"\n    {...props}\n  >\n    <path\n      fill=\"currentColor\"\n      d=\"M103,102.1388 C93.094,111.92 79.3504,118 64.1638,118 C48.8056,118 34.9294,111.768 25,101.7892 L25,95.2 C25,86.8096 31.981,80 40.6,80 L87.4,80 C96.019,80 103,86.8096 103,95.2 L103,102.1388 Z\"\n    />\n    <path\n      fill=\"currentColor\"\n      d=\"M63.9961647,24 C51.2938136,24 41,34.2938136 41,46.9961647 C41,59.7061864 51.2938136,70 63.9961647,70 C76.6985159,70 87,59.7061864 87,46.9961647 C87,34.2938136 76.6985159,24 63.9961647,24\"\n    />\n  </chakra.svg>\n)\n", "import { ImageProps, useImage } from \"@chakra-ui/image\"\nimport { chakra, SystemStyleObject } from \"@chakra-ui/system\"\nimport { cloneElement } from \"react\"\nimport { AvatarName } from \"./avatar-name\"\nimport { GenericAvatarIcon } from \"./generic-avatar-icon\"\n\ntype AvatarImageProps = ImageProps & {\n  getInitials?: (name: string) => string\n  borderRadius?: SystemStyleObject[\"borderRadius\"]\n  icon: React.ReactElement\n  iconLabel?: string\n  name?: string\n}\n\nexport function AvatarImage(props: AvatarImageProps) {\n  const {\n    src,\n    srcSet,\n    onError,\n    onLoad,\n    getInitials,\n    name,\n    borderRadius,\n    loading,\n    iconLabel,\n    icon = <GenericAvatarIcon />,\n    ignoreFallback,\n    referrerPolicy,\n    crossOrigin,\n  } = props\n\n  /**\n   * use the image hook to only show the image when it has loaded\n   */\n  const status = useImage({ src, onError, crossOrigin, ignoreFallback })\n\n  const hasLoaded = status === \"loaded\"\n\n  /**\n   * Fallback avatar applies under 2 conditions:\n   * - If `src` was passed and the image has not loaded or failed to load\n   * - If `src` wasn't passed\n   *\n   * In this case, we'll show either the name avatar or default avatar\n   */\n  const showFallback = !src || !hasLoaded\n\n  if (showFallback) {\n    return name ? (\n      <AvatarName\n        className=\"chakra-avatar__initials\"\n        getInitials={getInitials}\n        name={name}\n      />\n    ) : (\n      cloneElement(icon, {\n        role: \"img\",\n        \"aria-label\": iconLabel,\n      })\n    )\n  }\n\n  /**\n   * If `src` was passed and the image has loaded, we'll show it\n   */\n  return (\n    <chakra.img\n      src={src}\n      srcSet={srcSet}\n      alt={name}\n      onLoad={onLoad}\n      referrerPolicy={referrerPolicy}\n      crossOrigin={crossOrigin ?? undefined}\n      className=\"chakra-avatar__img\"\n      loading={loading}\n      __css={{\n        width: \"100%\",\n        height: \"100%\",\n        objectFit: \"cover\",\n        borderRadius,\n      }}\n    />\n  )\n}\n\nAvatarImage.displayName = \"AvatarImage\"\n", "import {\n  chakra,\n  forwardRef,\n  HTMLChakraProps,\n  omitThemingProps,\n  SystemStyleObject,\n  ThemingProps,\n  useMultiStyleConfig,\n} from \"@chakra-ui/system\"\nimport { callAllHandlers, cx, dataAttr } from \"@chakra-ui/shared-utils\"\nimport { AvatarStylesProvider } from \"./avatar-context\"\nimport { AvatarImage } from \"./avatar-image\"\nimport { GenericAvatarIcon } from \"./generic-avatar-icon\"\nimport { initials } from \"./avatar-name\"\nimport { AvatarOptions } from \"./avatar-types\"\nimport { useState } from \"react\"\n\nexport const baseStyle: SystemStyleObject = {\n  display: \"inline-flex\",\n  alignItems: \"center\",\n  justifyContent: \"center\",\n  textAlign: \"center\",\n  textTransform: \"uppercase\",\n  fontWeight: \"medium\",\n  position: \"relative\",\n  flexShrink: 0,\n}\n\nexport interface AvatarProps\n  extends Omit<HTMLChakraProps<\"span\">, \"onError\">,\n    AvatarOptions,\n    ThemingProps<\"Avatar\"> {\n  crossOrigin?: HTMLChakraProps<\"img\">[\"crossOrigin\"]\n  iconLabel?: string\n  /**\n   * If `true`, opt out of the avatar's `fallback` logic and\n   * renders the `img` at all times.\n   *\n   * @default false\n   */\n  ignoreFallback?: boolean\n}\n\n/**\n * Avatar component that renders an user avatar with\n * support for fallback avatar and name-only avatars\n */\nexport const Avatar = forwardRef<AvatarProps, \"span\">((props, ref) => {\n  const styles = useMultiStyleConfig(\"Avatar\", props)\n  const [isLoaded, setIsLoaded] = useState(false)\n\n  const {\n    src,\n    srcSet,\n    name,\n    showBorder,\n    borderRadius = \"full\",\n    onError,\n    onLoad: onLoadProp,\n    getInitials = initials,\n    icon = <GenericAvatarIcon />,\n    iconLabel = \" avatar\",\n    loading,\n    children,\n    borderColor,\n    ignoreFallback,\n    crossOrigin,\n    referrerPolicy,\n    ...rest\n  } = omitThemingProps(props)\n\n  const avatarStyles: SystemStyleObject = {\n    borderRadius,\n    borderWidth: showBorder ? \"2px\" : undefined,\n    ...baseStyle,\n    ...styles.container,\n  }\n\n  if (borderColor) {\n    avatarStyles.borderColor = borderColor\n  }\n\n  return (\n    <chakra.span\n      ref={ref}\n      {...rest}\n      className={cx(\"chakra-avatar\", props.className)}\n      data-loaded={dataAttr(isLoaded)}\n      __css={avatarStyles}\n    >\n      <AvatarStylesProvider value={styles}>\n        <AvatarImage\n          src={src}\n          srcSet={srcSet}\n          loading={loading}\n          onLoad={callAllHandlers(onLoadProp, () => {\n            setIsLoaded(true)\n          })}\n          onError={onError}\n          getInitials={getInitials}\n          name={name}\n          borderRadius={borderRadius}\n          icon={icon}\n          iconLabel={iconLabel}\n          ignoreFallback={ignoreFallback}\n          crossOrigin={crossOrigin}\n          referrerPolicy={referrerPolicy}\n        />\n        {children}\n      </AvatarStylesProvider>\n    </chakra.span>\n  )\n})\n\nAvatar.displayName = \"Avatar\"\n", "import {\n  chakra,\n  forwardRef,\n  omitThemingProps,\n  SystemProps,\n  SystemStyleObject,\n  ThemingProps,\n  useMultiStyleConfig,\n  HTMLChakraProps,\n} from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\nimport { getValidChildren } from \"@chakra-ui/react-children-utils\"\nimport { compact } from \"@chakra-ui/object-utils\"\nimport { baseStyle } from \"./avatar\"\nimport { cloneElement } from \"react\"\n\ninterface AvatarGroupOptions {\n  /**\n   * The children of the avatar group.\n   *\n   * Ideally should be `Avatar` and `MoreIndicator` components\n   */\n  children: React.ReactNode\n  /**\n   * The space between the avatars in the group.\n   * @default \"-0.75rem\"\n   * @type SystemProps[\"margin\"]\n   */\n  spacing?: SystemProps[\"margin\"]\n  /**\n   * The maximum number of visible avatars\n   */\n  max?: number\n}\n\nexport interface AvatarGroupProps\n  extends AvatarGroupOptions,\n    Omit<HTMLChakraProps<\"div\">, \"children\">,\n    ThemingProps<\"Avatar\"> {}\n\n/**\n * AvatarGroup displays a number of avatars grouped together in a stack.\n */\nexport const AvatarGroup = forwardRef<AvatarGroupProps, \"div\">(\n  function AvatarGroup(props, ref) {\n    const styles = useMultiStyleConfig(\"Avatar\", props)\n\n    const {\n      children,\n      borderColor,\n      max,\n      spacing = \"-0.75rem\",\n      borderRadius = \"full\",\n      ...rest\n    } = omitThemingProps(props)\n\n    const validChildren = getValidChildren(children)\n\n    /**\n     * get the avatars within the max\n     */\n    const childrenWithinMax =\n      max != null ? validChildren.slice(0, max) : validChildren\n\n    /**\n     * get the remaining avatar count\n     */\n    const excess = max != null ? validChildren.length - max : 0\n\n    /**\n     * Reversing the children is a great way to avoid using zIndex\n     * to overlap the avatars\n     */\n    const reversedChildren = childrenWithinMax.reverse()\n\n    const clones = reversedChildren.map((child, index) => {\n      const isFirstAvatar = index === 0\n\n      const childProps = {\n        marginEnd: isFirstAvatar ? 0 : spacing,\n        size: props.size,\n        borderColor: child.props.borderColor ?? borderColor,\n        showBorder: true,\n      }\n\n      return cloneElement(child, compact(childProps))\n    })\n\n    const groupStyles: SystemStyleObject = {\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"flex-end\",\n      flexDirection: \"row-reverse\",\n      ...styles.group,\n    }\n\n    const excessStyles: SystemStyleObject = {\n      borderRadius,\n      marginStart: spacing,\n      ...baseStyle,\n      ...styles.excessLabel,\n    }\n\n    return (\n      <chakra.div\n        ref={ref}\n        role=\"group\"\n        __css={groupStyles}\n        {...rest}\n        className={cx(\"chakra-avatar__group\", props.className)}\n      >\n        {excess > 0 && (\n          <chakra.span className=\"chakra-avatar__excess\" __css={excessStyles}>\n            {`+${excess}`}\n          </chakra.span>\n        )}\n        {clones}\n      </chakra.div>\n    )\n  },\n)\n\nAvatarGroup.displayName = \"AvatarGroup\"\n", "export function compact<T extends Record<any, any>>(object: T) {\n  const clone = Object.assign({}, object)\n  for (let key in clone) {\n    if (clone[key] === undefined) delete clone[key]\n  }\n  return clone\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,IAAM,CAAC,sBAAsB,eAAe,IAAI,cAErD;EACA,MAAM;EACN,UAAU;EACV,cAAc;AAChB,CAAC;;;ACkDK,yBAAA;AAhDN,IAAM,eAA0D;EAC9D,aAAa;IACX,KAAK;IACL,YAAY;IACZ,WAAW;EACb;EACA,WAAW;IACT,KAAK;IACL,UAAU;IACV,WAAW;EACb;EACA,gBAAgB;IACd,QAAQ;IACR,YAAY;IACZ,WAAW;EACb;EACA,cAAc;IACZ,QAAQ;IACR,UAAU;IACV,WAAW;EACb;AACF;AAUO,IAAM,cAAc;EACzB,SAASA,aAAY,OAAO,KAAK;AAC/B,UAAM,EAAE,YAAY,cAAc,WAAW,GAAG,KAAK,IAAI;AACzD,UAAM,SAAS,gBAAgB;AAE/B,UAAM,kBAAkB,aAAa,SAAS;AAE9C,UAAM,cAAiC;MACrC,UAAU;MACV,SAAS;MACT,YAAY;MACZ,gBAAgB;MAChB,GAAG;MACH,GAAG,OAAO;IACZ;AAEA,eACE;MAAC,OAAO;MAAP;QACC;QACC,GAAG;QACJ,WAAW,GAAG,wBAAwB,SAAS;QAC/C,OAAO;MAAA;IACT;EAEJ;AACF;AAEA,YAAY,cAAc;;;AC5CtB,IAAAC,sBAAA;AArBG,SAAS,SAAS,MAAc;AAJvC,MAAA;AAKE,QAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,QAAM,aAAY,KAAA,MAAM,CAAC,MAAP,OAAA,KAAY;AAC9B,QAAM,WAAW,MAAM,SAAS,IAAI,MAAM,MAAM,SAAS,CAAC,IAAI;AAC9D,SAAO,aAAa,WAChB,GAAG,UAAU,OAAO,CAAC,CAAA,GAAI,SAAS,OAAO,CAAC,CAAA,KAC1C,UAAU,OAAO,CAAC;AACxB;AASO,SAAS,WAAW,OAAwB;AACjD,QAAM,EAAE,MAAM,aAAa,GAAG,KAAK,IAAI;AACvC,QAAM,SAAS,gBAAgB;AAE/B,aACE,yBAAC,OAAO,KAAP,EAAW,MAAK,OAAM,cAAY,MAAO,GAAG,MAAM,OAAO,OAAO,OAC9D,UAAA,OAAO,eAAA,OAAA,SAAA,YAAc,IAAA,IAAQ,KAAA,CAChC;AAEJ;AAEA,WAAW,cAAc;;;ACxBvB,IAAAC,sBAAA;AADK,IAAM,oBAA4C,CAAC,cACxD;EAAC,OAAO;EAAP;IACC,SAAQ;IACR,OAAM;IACN,OAAM;IACN,QAAO;IACP,WAAU;IACT,GAAG;IAEJ,UAAA;UAAA;QAAC;QAAA;UACC,MAAK;UACL,GAAE;QAAA;MACJ;UACA;QAAC;QAAA;UACC,MAAK;UACL,GAAE;QAAA;MACJ;IAAA;EAAA;AACF;;;ACrBF,mBAA6B;AAuBlB,IAAAC,sBAAA;AAXJ,SAAS,YAAY,OAAyB;AACnD,QAAM;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,WAAO,yBAAC,mBAAA,CAAA,CAAkB;IAC1B;IACA;IACA;EACF,IAAI;AAKJ,QAAM,SAAS,SAAS,EAAE,KAAK,SAAS,aAAa,eAAe,CAAC;AAErE,QAAM,YAAY,WAAW;AAS7B,QAAM,eAAe,CAAC,OAAO,CAAC;AAE9B,MAAI,cAAc;AAChB,WAAO,WACL;MAAC;MAAA;QACC,WAAU;QACV;QACA;MAAA;IACF,QAEA,2BAAa,MAAM;MACjB,MAAM;MACN,cAAc;IAChB,CAAC;EAEL;AAKA,aACE;IAAC,OAAO;IAAP;MACC;MACA;MACA,KAAK;MACL;MACA;MACA,aAAa,eAAA,OAAA,cAAe;MAC5B,WAAU;MACV;MACA,OAAO;QACL,OAAO;QACP,QAAQ;QACR,WAAW;QACX;MACF;IAAA;EACF;AAEJ;AAEA,YAAY,cAAc;;;ACtE1B,IAAAC,gBAAyB;AA6Cd,IAAAC,sBAAA;AA3CJ,IAAM,YAA+B;EAC1C,SAAS;EACT,YAAY;EACZ,gBAAgB;EAChB,WAAW;EACX,eAAe;EACf,YAAY;EACZ,UAAU;EACV,YAAY;AACd;AAqBO,IAAM,SAAS,WAAgC,CAAC,OAAO,QAAQ;AACpE,QAAM,SAAS,oBAAoB,UAAU,KAAK;AAClD,QAAM,CAAC,UAAU,WAAW,QAAI,wBAAS,KAAK;AAE9C,QAAM;IACJ;IACA;IACA;IACA;IACA,eAAe;IACf;IACA,QAAQ;IACR,cAAc;IACd,WAAO,yBAAC,mBAAA,CAAA,CAAkB;IAC1B,YAAY;IACZ;IACA;IACA;IACA;IACA;IACA;IACA,GAAG;EACL,IAAI,iBAAiB,KAAK;AAE1B,QAAM,eAAkC;IACtC;IACA,aAAa,aAAa,QAAQ;IAClC,GAAG;IACH,GAAG,OAAO;EACZ;AAEA,MAAI,aAAa;AACf,iBAAa,cAAc;EAC7B;AAEA,aACE;IAAC,OAAO;IAAP;MACC;MACC,GAAG;MACJ,WAAW,GAAG,iBAAiB,MAAM,SAAS;MAC9C,eAAa,SAAS,QAAQ;MAC9B,OAAO;MAEP,cAAA,0BAAC,sBAAA,EAAqB,OAAO,QAC3B,UAAA;YAAA;UAAC;UAAA;YACC;YACA;YACA;YACA,QAAQ,gBAAgB,YAAY,MAAM;AACxC,0BAAY,IAAI;YAClB,CAAC;YACD;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UAAA;QACF;QACC;MAAA,EAAA,CACH;IAAA;EACF;AAEJ,CAAC;AAED,OAAO,cAAc;;;ACpGrB,IAAAC,gBAA6B;AA0FvB,IAAAC,sBAAA;ACxGC,SAAS,QAAoC,QAAW;AAC7D,QAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,MAAM;AACtC,WAAS,OAAO,OAAO;AACrB,QAAI,MAAM,GAAG,MAAM;AAAW,aAAO,MAAM,GAAG;EAChD;AACA,SAAO;AACT;ADqCO,IAAM,cAAc;EACzB,SAASC,aAAY,OAAO,KAAK;AAC/B,UAAM,SAAS,oBAAoB,UAAU,KAAK;AAElD,UAAM;MACJ;MACA;MACA;MACA,UAAU;MACV,eAAe;MACf,GAAG;IACL,IAAI,iBAAiB,KAAK;AAE1B,UAAM,gBAAgB,iBAAiB,QAAQ;AAK/C,UAAM,oBACJ,OAAO,OAAO,cAAc,MAAM,GAAG,GAAG,IAAI;AAK9C,UAAM,SAAS,OAAO,OAAO,cAAc,SAAS,MAAM;AAM1D,UAAM,mBAAmB,kBAAkB,QAAQ;AAEnD,UAAM,SAAS,iBAAiB,IAAI,CAAC,OAAO,UAAU;AA3E1D,UAAA;AA4EM,YAAM,gBAAgB,UAAU;AAEhC,YAAM,aAAa;QACjB,WAAW,gBAAgB,IAAI;QAC/B,MAAM,MAAM;QACZ,cAAa,KAAA,MAAM,MAAM,gBAAZ,OAAA,KAA2B;QACxC,YAAY;MACd;AAEA,iBAAO,4BAAa,OAAO,QAAQ,UAAU,CAAC;IAChD,CAAC;AAED,UAAM,cAAiC;MACrC,SAAS;MACT,YAAY;MACZ,gBAAgB;MAChB,eAAe;MACf,GAAG,OAAO;IACZ;AAEA,UAAM,eAAkC;MACtC;MACA,aAAa;MACb,GAAG;MACH,GAAG,OAAO;IACZ;AAEA,eACE;MAAC,OAAO;MAAP;QACC;QACA,MAAK;QACL,OAAO;QACN,GAAG;QACJ,WAAW,GAAG,wBAAwB,MAAM,SAAS;QAEpD,UAAA;UAAA,SAAS,SACR,yBAAC,OAAO,MAAP,EAAY,WAAU,yBAAwB,OAAO,cACnD,UAAA,IAAI,MAAA,GAAA,CACP;UAED;QAAA;MAAA;IACH;EAEJ;AACF;AAEA,YAAY,cAAc;", "names": ["AvatarBadge", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "import_react", "import_jsx_runtime", "import_react", "import_jsx_runtime", "AvatarGroup"]}