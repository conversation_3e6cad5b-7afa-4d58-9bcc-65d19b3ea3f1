{"version": 3, "sources": ["../../@chakra-ui/icon/src/icon.tsx", "../../@chakra-ui/icon/src/create-icon.tsx"], "sourcesContent": ["import {\n  chakra,\n  ChakraProps,\n  forwardRef,\n  SystemStyleObject,\n  useStyleConfig,\n} from \"@chakra-ui/system\"\nimport { cx } from \"@chakra-ui/shared-utils\"\n\nconst fallbackIcon = {\n  path: (\n    <g stroke=\"currentColor\" strokeWidth=\"1.5\">\n      <path\n        strokeLinecap=\"round\"\n        fill=\"none\"\n        d=\"M9,9a3,3,0,1,1,4,2.829,1.5,1.5,0,0,0-1,1.415V14.25\"\n      />\n      <path\n        fill=\"currentColor\"\n        strokeLinecap=\"round\"\n        d=\"M12,17.25a.375.375,0,1,0,.375.375A.375.375,0,0,0,12,17.25h0\"\n      />\n      <circle fill=\"none\" strokeMiterlimit=\"10\" cx=\"12\" cy=\"12\" r=\"11.25\" />\n    </g>\n  ),\n  viewBox: \"0 0 24 24\",\n}\n\ntype Orientation = \"vertical\" | \"horizontal\"\n\nexport interface IconProps\n  extends Omit<React.SVGAttributes<SVGElement>, keyof ChakraProps>,\n    ChakraProps {\n  orientation?: Orientation\n}\n\n/**\n * The Icon component renders as an svg element to help define your own custom components.\n *\n * @see Docs https://chakra-ui.com/docs/components/icon#using-the-icon-component\n */\nexport const Icon = forwardRef<IconProps, \"svg\">((props, ref) => {\n  const {\n    as: element,\n    viewBox,\n    color = \"currentColor\",\n    focusable = false,\n    children,\n    className,\n    __css,\n    ...rest\n  } = props\n\n  const _className = cx(\"chakra-icon\", className)\n  const customStyles = useStyleConfig(\"Icon\", props)\n\n  const styles: SystemStyleObject = {\n    w: \"1em\",\n    h: \"1em\",\n    display: \"inline-block\",\n    lineHeight: \"1em\",\n    flexShrink: 0,\n    color,\n    ...__css,\n    ...customStyles,\n  }\n\n  const shared: any = {\n    ref,\n    focusable,\n    className: _className,\n    __css: styles,\n  }\n\n  const _viewBox = viewBox ?? fallbackIcon.viewBox\n\n  /**\n   * If you're using an icon library like `react-icons`.\n   * Note: anyone passing the `as` prop, should manage the `viewBox` from the external component\n   */\n  if (element && typeof element !== \"string\") {\n    return <chakra.svg as={element} {...shared} {...rest} />\n  }\n\n  const _path = (children ?? fallbackIcon.path) as React.ReactNode\n\n  return (\n    <chakra.svg verticalAlign=\"middle\" viewBox={_viewBox} {...shared} {...rest}>\n      {_path}\n    </chakra.svg>\n  )\n})\n\nIcon.displayName = \"Icon\"\n\nexport default Icon\n", "import { forwardRef } from \"@chakra-ui/system\"\nimport { Children } from \"react\"\nimport { Icon, IconProps } from \"./icon\"\n\ninterface CreateIconOptions {\n  /**\n   * The icon `svg` viewBox\n   * @default \"0 0 24 24\"\n   */\n  viewBox?: string\n  /**\n   * The `svg` path or group element\n   * @type React.ReactElement | React.ReactElement[]\n   */\n  path?: React.ReactElement | React.ReactElement[]\n  /**\n   * If the `svg` has a single path, simply copy the path's `d` attribute\n   */\n  d?: string\n  /**\n   * The display name useful in the dev tools\n   */\n  displayName?: string\n  /**\n   * Default props automatically passed to the component; overwritable\n   */\n  defaultProps?: IconProps\n}\n\nexport function createIcon(options: CreateIconOptions) {\n  const {\n    viewBox = \"0 0 24 24\",\n    d: pathDefinition,\n    displayName,\n    defaultProps = {},\n  } = options\n  const path = Children.toArray(options.path)\n\n  const Comp = forwardRef<IconProps, \"svg\">((props, ref) => (\n    <Icon ref={ref} viewBox={viewBox} {...defaultProps} {...props}>\n      {path.length ? path : <path fill=\"currentColor\" d={pathDefinition} />}\n    </Icon>\n  ))\n\n  Comp.displayName = displayName\n\n  return Comp\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAWI,yBAAA;AAFJ,IAAM,eAAe;EACnB,UACE,yBAAC,KAAA,EAAE,QAAO,gBAAe,aAAY,OACnC,UAAA;QAAA;MAAC;MAAA;QACC,eAAc;QACd,MAAK;QACL,GAAE;MAAA;IACJ;QACA;MAAC;MAAA;QACC,MAAK;QACL,eAAc;QACd,GAAE;MAAA;IACJ;QACA,wBAAC,UAAA,EAAO,MAAK,QAAO,kBAAiB,MAAK,IAAG,MAAK,IAAG,MAAK,GAAE,QAAA,CAAQ;EAAA,EAAA,CACtE;EAEF,SAAS;AACX;AAeO,IAAM,OAAO,WAA6B,CAAC,OAAO,QAAQ;AAC/D,QAAM;IACJ,IAAI;IACJ;IACA,QAAQ;IACR,YAAY;IACZ;IACA;IACA;IACA,GAAG;EACL,IAAI;AAEJ,QAAM,aAAa,GAAG,eAAe,SAAS;AAC9C,QAAM,eAAe,eAAe,QAAQ,KAAK;AAEjD,QAAM,SAA4B;IAChC,GAAG;IACH,GAAG;IACH,SAAS;IACT,YAAY;IACZ,YAAY;IACZ;IACA,GAAG;IACH,GAAG;EACL;AAEA,QAAM,SAAc;IAClB;IACA;IACA,WAAW;IACX,OAAO;EACT;AAEA,QAAM,WAAW,WAAA,OAAA,UAAW,aAAa;AAMzC,MAAI,WAAW,OAAO,YAAY,UAAU;AAC1C,eAAO,wBAAC,OAAO,KAAP,EAAW,IAAI,SAAU,GAAG,QAAS,GAAG,KAAA,CAAM;EACxD;AAEA,QAAM,QAAS,YAAA,OAAA,WAAY,aAAa;AAExC,aACE,wBAAC,OAAO,KAAP,EAAW,eAAc,UAAS,SAAS,UAAW,GAAG,QAAS,GAAG,MACnE,UAAA,MAAA,CACH;AAEJ,CAAC;AAED,KAAK,cAAc;;;AC5FnB,mBAAyB;AAuCG,IAAAA,sBAAA;", "names": ["import_jsx_runtime"]}